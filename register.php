<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Addis Ababa Stadium Tickets</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        /* Register Page Specific Styles */
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a6fd8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .error-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
            border-left: 4px solid #dc3545;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }

        .registration-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }

        .registration-form {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px;
            backdrop-filter: blur(10px);
        }

        .registration-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .registration-header h2 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .registration-header p {
            color: #666;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group label.required::after {
            content: ' *';
            color: #dc3545;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group.error .form-control {
            border-color: var(--error-color);
            background: #fff5f5;
        }

        .form-group.error .error-message {
            display: block;
        }

        .error-message {
            color: var(--error-color);
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }

        .phone-input-group {
            display: flex;
            align-items: center;
        }

        .phone-prefix {
            background: #e9ecef;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-right: none;
            border-radius: 10px 0 0 10px;
            font-weight: 500;
            color: #495057;
        }

        .phone-input {
            border-radius: 0 10px 10px 0 !important;
            border-left: none !important;
        }

        .phone-input:focus {
            border-left: 2px solid var(--primary-color) !important;
        }

        .form-group input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        .form-footer {
            text-align: center;
            margin-top: 30px;
        }

        .submit-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .login-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        footer {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            text-align: center;
            padding: 20px;
            margin-top: auto;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .registration-form {
                padding: 30px 20px;
                margin: 20px;
            }

            .registration-header h2 {
                font-size: 24px;
            }

            .phone-input-group {
                flex-direction: column;
            }

            .phone-prefix {
                border-radius: 10px 10px 0 0;
                border-right: 2px solid #e1e5e9;
                border-bottom: none;
                width: 100%;
                text-align: center;
            }

            .phone-input {
                border-radius: 0 0 var(--border-radius) var(--border-radius) !important;
                border-left: 2px solid var(--border-color) !important;
                border-top: none !important;
            }
        }
    </style>
</head>
<body>
    <?php if (isset($_GET['error'])): ?>
    <div class="error-alert">
        <?php
        if ($_GET['error'] == 'dberror') {
            echo "Registration failed. Please try again later.";
            if (isset($_GET['details'])) {
                echo "<br><small>Details: " . htmlspecialchars($_GET['details']) . "</small>";
            }
        } elseif ($_GET['error'] == 'emailtaken') {
            echo "This email is already registered. Please use a different email.";
        } elseif ($_GET['error'] == 'emptyfields') {
            echo "Please fill in all required fields.";
        } elseif ($_GET['error'] == 'invalidemail') {
            echo "Please enter a valid email address.";
        } elseif ($_GET['error'] == 'weakpassword') {
            echo "Password does not meet requirements.";
            if (isset($_GET['details'])) {
                echo "<br><small>" . htmlspecialchars($_GET['details']) . "</small>";
            }
        } elseif ($_GET['error'] == 'invalidgender') {
            echo "Please select a valid gender.";
        } else {
            echo "Registration error: " . htmlspecialchars($_GET['error']);
        }
        ?>
    </div>
    <?php endif; ?>
    
    <!-- Header -->
    <header>
        <div class="top-bar">
            <p>Register to book tickets for Addis Ababa Stadium events</p>
        </div>
        <nav class="navbar">
            <div class="logo">
                <img src="logo.png" alt="Addis Ababa Stadium Logo">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
        </nav>
    </header>
    
    <!-- Registration Form -->
    <div class="registration-container">
        <div class="registration-header">
            <h2>Create Your Account</h2>
            <p>Register to book tickets for exciting events at Addis Ababa Stadium</p>
        </div>
        
        <form id="registrationForm" class="registration-form" action="register_handler.php" method="POST">
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName" class="required">First Name</label>
                    <input type="text" id="firstName" name="firstName" class="form-control" required>
                    <div class="error-message">Please enter your first name</div>
                </div>
                
                <div class="form-group">
                    <label for="lastName" class="required">Last Name</label>
                    <input type="text" id="lastName" name="lastName" class="form-control" required>
                    <div class="error-message">Please enter your last name</div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="email" class="required">Email Address</label>
                    <input type="email" id="email" name="email" class="form-control" required>
                    <div class="error-message">Please enter a valid email address</div>
                </div>
                
                <div class="form-group">
                    <label for="phone" class="required">Phone Number</label>
                    <div class="phone-input-group">
                        <span class="phone-prefix">+251</span>
                        <input type="tel" id="phone" name="phone" class="form-control phone-input" required pattern="[0-9]{9}" maxlength="9">
                    </div>
                    <div class="error-message">Please enter a valid 9-digit Ethiopian phone number</div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="password" class="required">Password</label>
                    <input type="password" id="password" name="password" class="form-control" required minlength="8">
                    <div class="error-message">Password must be at least 8 characters</div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword" class="required">Confirm Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" required>
                    <div class="error-message">Passwords do not match</div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="gender" class="required">Gender</label>
                    <select id="gender" name="gender" class="form-control" required>
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>
                    <div class="error-message">Please select your gender</div>
                </div>
                
                <div class="form-group">
                    <label for="dob" class="required">Date of Birth</label>
                    <input type="date" id="dob" name="dob" class="form-control" required>
                    <div class="error-message">Please enter your date of birth</div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="subcity">Subcity (Addis Ababa)</label>
                    <select id="subcity" name="subcity" class="form-control">
                        <option value="">Select Subcity</option>
                        <option value="Arada">Arada</option>
                        <option value="Kirkos">Kirkos</option>
                        <option value="Kolfe Keranio">Kolfe Keranio</option>
                        <option value="Lideta">Lideta</option>
                        <option value="Nifas Silk">Nifas Silk</option>
                        <option value="Addis Ketema">Addis Ketema</option>
                        <option value="Bole">Bole</option>
                        <option value="Gulele">Gulele</option>
                        <option value="Yeka">Yeka</option>
                        <option value="Akaki Kaliti">Akaki Kaliti</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="houseNumber">House Number</label>
                    <input type="text" id="houseNumber" name="houseNumber" class="form-control">
                </div>
            </div>
            
            <div class="form-group">
                <input type="checkbox" id="terms" name="terms" required>
                <label for="terms">I agree to the <a href="#" style="color: var(--primary-color);">Terms of Service</a> and <a href="#" style="color: var(--primary-color);">Privacy Policy</a></label>
                <div class="error-message">You must agree to the terms</div>
            </div>
            
            <div class="form-footer">
                <button type="submit" class="submit-btn">Create Account</button>
                <div class="login-link">
                    Already have an account? <a href="login.php">Log in</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Footer -->
    <footer>
        <div class="footer-bottom">
            <p>&copy; 2025 Addis Tickets. All Rights Reserved.</p>
        </div>
    </footer>
    
    <script>
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            // Reset error states
            document.querySelectorAll('.form-group').forEach(group => {
                group.classList.remove('error');
            });
            
            // Validate form
            let isValid = true;
            
            // Required fields validation
            const requiredFields = [
                'firstName', 'lastName', 'email', 'phone', 
                'password', 'confirmPassword', 'gender', 'dob'
            ];
            
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value) {
                    field.closest('.form-group').classList.add('error');
                    isValid = false;
                }
            });
            
            // Terms checkbox validation
            const terms = document.getElementById('terms');
            if (!terms.checked) {
                terms.closest('.form-group').classList.add('error');
                isValid = false;
            }
            
            // Email validation
            const email = document.getElementById('email');
            if (email.value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
                email.closest('.form-group').classList.add('error');
                isValid = false;
            }
            
            // Phone validation (Ethiopian format)
            const phone = document.getElementById('phone');
            if (phone.value && !/^[0-9]{9}$/.test(phone.value)) {
                phone.closest('.form-group').classList.add('error');
                isValid = false;
            }
            
            // Password confirmation
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirmPassword');
            if (password.value !== confirmPassword.value) {
                confirmPassword.closest('.form-group').classList.add('error');
                isValid = false;
            }
            
            // Password length
            if (password.value && password.value.length < 8) {
                password.closest('.form-group').classList.add('error');
                isValid = false;
            }
            
            // If not valid, prevent form submission
            if (!isValid) {
                e.preventDefault();
            }
            // If valid, allow form submission to proceed to PHP handler
        });

        // Real-time validation for password match
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            const formGroup = this.closest('.form-group');
            
            if (password && confirmPassword && password !== confirmPassword) {
                formGroup.classList.add('error');
            } else {
                formGroup.classList.remove('error');
            }
        });

        // Ethiopian phone number formatting
        document.getElementById('phone').addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    </script>
</body>
</html>