<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

$message = '';
$error = '';

// Handle feedback submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        try {
            // Get and validate form data
            $name = sanitizeInput($_POST['name']);
            $email = sanitizeInput($_POST['email'], 'email');
            $category = sanitizeInput($_POST['category']);
            $rating = intval($_POST['rating'] ?? 0);
            $subject = sanitizeInput($_POST['subject']);
            $feedbackMessage = sanitizeInput($_POST['message']);
            $eventId = sanitizeInput($_POST['event_id'] ?? '');
            
            // Validate required fields
            if (!$name || !$email || !$category || !$subject || !$feedbackMessage) {
                throw new Exception("All required fields must be filled");
            }
            
            if ($rating < 1 || $rating > 5) {
                throw new Exception("Please provide a valid rating between 1 and 5");
            }
            
            // Generate unique ticket ID
            $ticketId = 'FB_' . date('YmdHis') . '_' . substr(bin2hex(random_bytes(4)), 0, 8);
            
            // Get user ID if logged in
            $userId = $_SESSION['user_id'] ?? null;
            
            // Insert feedback into support tickets table
            // After running fix_support_table.php, the table should have Name column
            $fullMessage = "Rating: $rating/5 stars\n\n" . $feedbackMessage;
            if ($eventId) {
                $fullMessage = "Event ID: $eventId\n" . $fullMessage;
            }

            // Try with Name column first (after fix_support_table.php)
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO SupportTickets (TicketId, UserId, Name, Subject, Message, Category, Status, Priority)
                    VALUES (?, ?, ?, ?, ?, ?, 'Open', 'Medium')
                ");
                $params = [$ticketId, $userId, $name, $subject, $fullMessage, $category];
            } catch (Exception $e) {
                // Fallback if Name column still doesn't exist
                $fullMessage = "Name: $name\nEmail: $email\n" . $fullMessage;
                $stmt = $pdo->prepare("
                    INSERT INTO SupportTickets (TicketId, UserId, Subject, Message, Category, Status, Priority)
                    VALUES (?, ?, ?, ?, ?, 'Open', 'Medium')
                ");
                $params = [$ticketId, $userId, $subject, $fullMessage, $category];
            }

            $stmt->execute($params);
            
            // Log activity if user is logged in
            if ($userId) {
                logActivity($userId, 'User', 'Feedback Submitted', 'SupportTickets', $ticketId);
            }
            
            $message = "Thank you for your feedback! Your ticket ID is: $ticketId";
            
            // Clear form data on success
            $_POST = [];
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get recent events for feedback dropdown
try {
    $eventsStmt = $pdo->prepare("
        SELECT EventId, Title, EventDate 
        FROM Events 
        WHERE IsApproved = 1 
        ORDER BY EventDate DESC 
        LIMIT 20
    ");
    $eventsStmt->execute();
    $recentEvents = $eventsStmt->fetchAll();
} catch (Exception $e) {
    $recentEvents = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            text-decoration: none;
            color: #667eea;
        }
        
        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
        
        .logo-text h1 {
            font-size: 1.5em;
            margin-bottom: 2px;
            font-weight: 800;
        }
        
        .logo-text p {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-links a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: #667eea;
        }
        
        .feedback-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .feedback-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        
        .feedback-header h1 {
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #333;
            font-weight: 800;
        }
        
        .feedback-header p {
            font-size: 1.1em;
            color: #666;
            line-height: 1.6;
        }
        
        .feedback-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .rating-group {
            margin-bottom: 25px;
        }
        
        .rating-stars {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .star {
            font-size: 2em;
            color: #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .star:hover,
        .star.active {
            color: #ffd700;
            transform: scale(1.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            margin-right: 15px;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
        }
        
        .alert {
            padding: 20px;
            margin-bottom: 25px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .feedback-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feedback-type {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .feedback-type:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-5px);
            border-color: #667eea;
        }
        
        .feedback-type i {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .feedback-type h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .feedback-type p {
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                display: none;
            }
            
            .feedback-header,
            .feedback-form {
                padding: 25px;
            }
            
            .feedback-header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <a href="Finalll_updated.php" class="logo">
                <img alt="Addis Tickets Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </a>
            <ul class="nav-links">
                <li><a href="Finalll_updated.php">Home</a></li>
                <li><a href="search_events.php">Events</a></li>
                <?php if (isset($_SESSION['user_id'])): ?>
                    <li><a href="profile.php">Profile</a></li>
                    <li><a href="tickets.php">My Tickets</a></li>
                <?php endif; ?>
                <li><a href="support.php">Support</a></li>
                <li><a href="feedback.php" class="active">Feedback</a></li>
                <?php if (!isset($_SESSION['user_id'])): ?>
                    <li><a href="login.php">Login</a></li>
                <?php else: ?>
                    <li><a href="logout.php">Logout</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </nav>

    <div class="feedback-container">
        <div class="feedback-header">
            <h1><i class="fas fa-comments"></i> Share Your Feedback</h1>
            <p>Your opinion matters to us! Help us improve our service by sharing your experience, suggestions, or reporting any issues you've encountered.</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="feedback-types">
            <div class="feedback-type" onclick="setFeedbackType('general')">
                <i class="fas fa-comment-alt"></i>
                <h3>General Feedback</h3>
                <p>Share your overall experience with our platform</p>
            </div>
            <div class="feedback-type" onclick="setFeedbackType('event')">
                <i class="fas fa-calendar-star"></i>
                <h3>Event Experience</h3>
                <p>Tell us about a specific event you attended</p>
            </div>
            <div class="feedback-type" onclick="setFeedbackType('suggestion')">
                <i class="fas fa-lightbulb"></i>
                <h3>Suggestions</h3>
                <p>Share ideas for new features or improvements</p>
            </div>
            <div class="feedback-type" onclick="setFeedbackType('issue')">
                <i class="fas fa-bug"></i>
                <h3>Report Issue</h3>
                <p>Let us know about technical problems or bugs</p>
            </div>
        </div>

        <form method="POST" class="feedback-form">
            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
            
            <div class="form-row">
                <div class="form-group">
                    <label for="name">Full Name *</label>
                    <input type="text" id="name" name="name" value="<?= htmlspecialchars($_POST['name'] ?? $_SESSION['first_name'] ?? '') ?>" required>
                </div>
                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($_POST['email'] ?? $_SESSION['user_email'] ?? '') ?>" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="category">Feedback Category *</label>
                    <select id="category" name="category" required>
                        <option value="">Select Category</option>
                        <option value="feedback" <?= ($_POST['category'] ?? '') === 'feedback' ? 'selected' : '' ?>>General Feedback</option>
                        <option value="booking" <?= ($_POST['category'] ?? '') === 'booking' ? 'selected' : '' ?>>Booking Experience</option>
                        <option value="payment" <?= ($_POST['category'] ?? '') === 'payment' ? 'selected' : '' ?>>Payment Issues</option>
                        <option value="technical" <?= ($_POST['category'] ?? '') === 'technical' ? 'selected' : '' ?>>Technical Issues</option>
                        <option value="general" <?= ($_POST['category'] ?? '') === 'general' ? 'selected' : '' ?>>Suggestions</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="event_id">Related Event (Optional)</label>
                    <select id="event_id" name="event_id">
                        <option value="">Select Event</option>
                        <?php foreach ($recentEvents as $event): ?>
                            <option value="<?= htmlspecialchars($event['EventId']) ?>" 
                                    <?= ($_POST['event_id'] ?? '') === $event['EventId'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($event['Title']) ?> - <?= date('M d, Y', strtotime($event['EventDate'])) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="rating-group">
                <label>Overall Rating *</label>
                <div class="rating-stars">
                    <span class="star" data-rating="1">★</span>
                    <span class="star" data-rating="2">★</span>
                    <span class="star" data-rating="3">★</span>
                    <span class="star" data-rating="4">★</span>
                    <span class="star" data-rating="5">★</span>
                </div>
                <input type="hidden" id="rating" name="rating" value="<?= $_POST['rating'] ?? '' ?>" required>
            </div>

            <div class="form-group">
                <label for="subject">Subject *</label>
                <input type="text" id="subject" name="subject" value="<?= htmlspecialchars($_POST['subject'] ?? '') ?>" placeholder="Brief summary of your feedback" required>
            </div>

            <div class="form-group">
                <label for="message">Your Feedback *</label>
                <textarea id="message" name="message" placeholder="Please share your detailed feedback, suggestions, or describe any issues you've experienced..." required><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="support.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Support
                </a>
                <button type="submit" class="btn">
                    <i class="fas fa-paper-plane"></i> Submit Feedback
                </button>
            </div>
        </form>
    </div>

    <script>
        // Rating system
        const stars = document.querySelectorAll('.star');
        const ratingInput = document.getElementById('rating');
        
        stars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.dataset.rating;
                ratingInput.value = rating;
                
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
            
            star.addEventListener('mouseover', function() {
                const rating = this.dataset.rating;
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.style.color = '#ffd700';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        document.querySelector('.rating-stars').addEventListener('mouseleave', function() {
            const currentRating = ratingInput.value;
            stars.forEach((s, index) => {
                if (index < currentRating) {
                    s.style.color = '#ffd700';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
        
        // Set initial rating if exists
        const initialRating = ratingInput.value;
        if (initialRating) {
            stars.forEach((s, index) => {
                if (index < initialRating) {
                    s.classList.add('active');
                }
            });
        }
        
        // Feedback type selection
        function setFeedbackType(type) {
            const categorySelect = document.getElementById('category');
            const subjectInput = document.getElementById('subject');
            
            switch(type) {
                case 'general':
                    categorySelect.value = 'feedback';
                    subjectInput.value = 'General Platform Feedback';
                    break;
                case 'event':
                    categorySelect.value = 'booking';
                    subjectInput.value = 'Event Experience Feedback';
                    break;
                case 'suggestion':
                    categorySelect.value = 'general';
                    subjectInput.value = 'Feature Suggestion';
                    break;
                case 'issue':
                    categorySelect.value = 'technical';
                    subjectInput.value = 'Technical Issue Report';
                    break;
            }
        }
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const rating = document.getElementById('rating').value;
            if (!rating) {
                e.preventDefault();
                alert('Please provide a rating before submitting your feedback.');
                return false;
            }
        });
    </script>
</body>
</html>
