<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Initialization - Event Ticketing System</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; color: #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .btn { padding: 15px 30px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: 600; text-decoration: none; display: inline-block; }
        .btn:hover { background: #5a6fd8; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .step { margin: 20px 0; padding: 20px; border-left: 4px solid #667eea; background: #f8f9fa; }
        .step h3 { margin-bottom: 10px; color: #667eea; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 5px; font-family: 'Courier New', monospace; margin: 15px 0; overflow-x: auto; }
        .status { display: inline-block; padding: 5px 10px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Clean Database Setup</h1>
            <p>Event Ticketing System - Fresh Installation</p>
        </div>

        <?php
        $step = $_GET['step'] ?? 'check';
        $message = '';
        $error = '';
        $success = false;

        // Database configuration
        $host = 'localhost';
        $username = 'root';
        $password = '';
        $dbname = 'eventbb';

        if ($step === 'initialize') {
            try {
                // Step 1: Connect to MySQL server (without database)
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                ]);

                // Step 2: Read and execute SQL file
                $sqlFile = 'clean_database_setup.sql';
                if (!file_exists($sqlFile)) {
                    throw new Exception("SQL file not found: $sqlFile");
                }

                $sql = file_get_contents($sqlFile);
                if ($sql === false) {
                    throw new Exception("Could not read SQL file");
                }

                // Step 3: Execute SQL commands
                $statements = explode(';', $sql);
                $executedCount = 0;
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement) && !preg_match('/^--/', $statement)) {
                        try {
                            $pdo->exec($statement);
                            $executedCount++;
                        } catch (PDOException $e) {
                            // Skip comments and empty statements
                            if (!preg_match('/Table.*already exists|Database.*already exists/', $e->getMessage())) {
                                error_log("SQL Error: " . $e->getMessage() . " in statement: " . substr($statement, 0, 100));
                            }
                        }
                    }
                }

                $success = true;
                $message = "Database initialized successfully! Executed $executedCount SQL statements.";

            } catch (Exception $e) {
                $error = "Database initialization failed: " . $e->getMessage();
            }
        }

        // Check current database status
        $dbStatus = [];
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);

            // Check tables
            $tables = [
                'Users' => 'User accounts',
                'EventOrganizers' => 'Event organizers',
                'AdminUsers' => 'Admin accounts',
                'Events' => 'Event listings',
                'Tickets' => 'Ticket inventory',
                'Bookings' => 'Customer bookings',
                'Payments' => 'Payment records',
                'SupportTickets' => 'Support system',
                'FAQ' => 'FAQ entries',
                'SystemSettings' => 'System configuration',
                'AuditLog' => 'Activity logging',
                'UserSessions' => 'Session management'
            ];

            foreach ($tables as $table => $description) {
                try {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                        $count = $countStmt->fetch()['count'];
                        $dbStatus[$table] = ['exists' => true, 'count' => $count, 'description' => $description];
                    } else {
                        $dbStatus[$table] = ['exists' => false, 'count' => 0, 'description' => $description];
                    }
                } catch (Exception $e) {
                    $dbStatus[$table] = ['exists' => false, 'count' => 0, 'description' => $description];
                }
            }

        } catch (Exception $e) {
            $dbConnectionError = $e->getMessage();
        }
        ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <strong>✅ Success!</strong> <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <strong>❌ Error!</strong> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if (isset($dbConnectionError)): ?>
            <div class="alert alert-warning">
                <strong>⚠️ Database Connection Issue:</strong> <?= htmlspecialchars($dbConnectionError) ?>
                <br><br>
                <strong>Please ensure:</strong>
                <ul style="margin-top: 10px;">
                    <li>MySQL/MariaDB server is running</li>
                    <li>Database credentials are correct</li>
                    <li>Database user has proper permissions</li>
                </ul>
            </div>
        <?php endif; ?>

        <div class="card">
            <h2>📋 Database Status</h2>
            
            <?php if (isset($dbStatus) && !empty($dbStatus)): ?>
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Table</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Description</th>
                            <th style="padding: 12px; text-align: center; border-bottom: 2px solid #dee2e6;">Status</th>
                            <th style="padding: 12px; text-align: center; border-bottom: 2px solid #dee2e6;">Records</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($dbStatus as $table => $info): ?>
                        <tr>
                            <td style="padding: 10px; border-bottom: 1px solid #dee2e6; font-weight: 600;"><?= $table ?></td>
                            <td style="padding: 10px; border-bottom: 1px solid #dee2e6;"><?= $info['description'] ?></td>
                            <td style="padding: 10px; border-bottom: 1px solid #dee2e6; text-align: center;">
                                <span class="status <?= $info['exists'] ? 'status-success' : 'status-error' ?>">
                                    <?= $info['exists'] ? '✅ EXISTS' : '❌ MISSING' ?>
                                </span>
                            </td>
                            <td style="padding: 10px; border-bottom: 1px solid #dee2e6; text-align: center;">
                                <?= number_format($info['count']) ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <div class="card">
            <h2>🚀 Initialization Steps</h2>
            
            <div class="step">
                <h3>Step 1: Database Configuration</h3>
                <p>Update database credentials in the configuration files:</p>
                <div class="code-block">
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'eventbb';
                </div>
            </div>

            <div class="step">
                <h3>Step 2: Initialize Clean Database</h3>
                <p>Click the button below to create a fresh database with no pre-entered data:</p>
                <br>
                <a href="?step=initialize" class="btn btn-success">
                    🗄️ Initialize Clean Database
                </a>
            </div>

            <div class="step">
                <h3>Step 3: Verify Installation</h3>
                <p>After initialization, all tables should show "EXISTS" status above.</p>
            </div>

            <div class="step">
                <h3>Step 4: Access Your System</h3>
                <p>Once initialized, you can access:</p>
                <ul style="margin-top: 10px;">
                    <li><strong>Main Site:</strong> <a href="Finalll_updated.php">Finalll_updated.php</a></li>
                    <li><strong>User Registration:</strong> <a href="register.php">register.php</a></li>
                    <li><strong>Organizer Registration:</strong> <a href="organizer_login.php">organizer_login.php</a></li>
                    <li><strong>Admin Panel:</strong> Create admin account manually</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h2>📝 Important Notes</h2>
            <div class="alert alert-warning">
                <strong>🔒 Security Notice:</strong>
                <ul style="margin-top: 10px;">
                    <li>This is a CLEAN database with NO pre-entered users</li>
                    <li>You will need to register the first admin user manually</li>
                    <li>Change default database credentials in production</li>
                    <li>Enable SSL/HTTPS for production deployment</li>
                    <li>Configure proper backup procedures</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h2>🔧 Manual Admin Creation</h2>
            <p>To create the first admin user, run this SQL command in your database:</p>
            <div class="code-block">
INSERT INTO AdminUsers (AdminId, Username, Email, Password, FullName, Role) 
VALUES (
    'ADMIN001', 
    'admin', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    'System Administrator', 
    'SuperAdmin'
);
            </div>
            <p><small><strong>Default Password:</strong> password (Please change immediately after login)</small></p>
        </div>
    </div>
</body>
</html>
