<?php
session_start();

// Include secure database connection
require_once 'db_connect.php';
require_once 'security_config.php';
require_once 'email_system.php';

// Convert PDO to mysqli for compatibility (temporary)
$host = 'localhost';
$user = 'root';
$pass = '';
$db = 'eventbb';

$conn = new mysqli($host, $user, $pass, $db);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Validate and sanitize inputs
        $firstName = filter_input(INPUT_POST, 'firstName', FILTER_SANITIZE_STRING);
        $lastName = filter_input(INPUT_POST, 'lastName', FILTER_SANITIZE_STRING);
        $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
        $phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_STRING);
        $password = $_POST['password'] ?? '';
        $gender = filter_input(INPUT_POST, 'gender', FILTER_SANITIZE_STRING);
        $dob = filter_input(INPUT_POST, 'dob', FILTER_SANITIZE_STRING);
        $subcity = filter_input(INPUT_POST, 'subcity', FILTER_SANITIZE_STRING);
        $houseNumber = filter_input(INPUT_POST, 'houseNumber', FILTER_VALIDATE_INT);
        $city = "Addis Ababa";

        // Validate required fields
        if (!$firstName || !$lastName || !$email || !$phone || !$password || !$gender || !$dob) {
            header("Location: register.php?error=emptyfields");
            exit();
        }

        // Validate email format
        if (!$email) {
            header("Location: register.php?error=invalidemail");
            exit();
        }

        // Enhanced password validation
        $passwordErrors = validatePassword($password);
        if (!empty($passwordErrors)) {
            header("Location: register.php?error=weakpassword&details=" . urlencode(implode(', ', $passwordErrors)));
            exit();
        }

        // Validate gender
        if (!in_array($gender, ['Male', 'Female'])) {
            header("Location: register.php?error=invalidgender");
            exit();
        }

        // Format phone number
        $phone = "+251" . preg_replace('/[^0-9]/', '', $phone);

        // Hash password
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        // Generate UserId and verification token
        $userId = 'USR' . substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 6);
        $verificationToken = bin2hex(random_bytes(32));
        $tokenExpiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

        // Check if email already exists
        $email_check = $conn->prepare("SELECT * FROM Users WHERE Email = ?");
        $email_check->bind_param("s", $email);
        $email_check->execute();
        $result = $email_check->get_result();

        if ($result->num_rows > 0) {
            $email_check->close();
            header("Location: register.php?error=emailtaken");
            exit();
        }
        $email_check->close();

        // Prepare and bind (including email verification fields)
        $stmt = $conn->prepare("INSERT INTO Users (UserId, FirstName, LastName, Gender, DateOfBirth, PhoneNumber, Email, PasswordHash, HouseNumber, SubCity, City, EmailVerified, EmailVerificationToken, TokenExpiry) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?)");
        $stmt->bind_param("ssssssssissss", $userId, $firstName, $lastName, $gender, $dob, $phone, $email, $passwordHash, $houseNumber, $subcity, $city, $verificationToken, $tokenExpiry);

        // Execute the statement
        if ($stmt->execute()) {
            // Send verification email
            $verificationLink = "http://" . $_SERVER['HTTP_HOST'] . "/email_verification.php?token=" . $verificationToken;
            $emailSent = sendVerificationEmail($email, $firstName, $verificationLink);

            $stmt->close();
            $conn->close();

            if ($emailSent) {
                header("Location: registration_success.php?email=" . urlencode($email));
            } else {
                header("Location: registration_success.php?email=" . urlencode($email) . "&email_error=1");
            }
            exit();
        } else {
            $stmt->close();
            $conn->close();
            header("Location: register.php?error=dberror");
            exit();
        }

    } catch (Exception $e) {
        error_log("Registration error: " . $e->getMessage());

        // Log detailed error for debugging
        try {
            $debugEmail = $email ?? 'unknown';
            $debugErrorType = 'exception';
            $debugErrorMessage = $e->getMessage();
            $debugUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $debugIpAddress = $_SERVER['REMOTE_ADDR'] ?? '';

            $debugStmt = $conn->prepare("INSERT INTO RegistrationDebug (Email, ErrorType, ErrorMessage, UserAgent, IpAddress) VALUES (?, ?, ?, ?, ?)");
            $debugStmt->bind_param("sssss", $debugEmail, $debugErrorType, $debugErrorMessage, $debugUserAgent, $debugIpAddress);
            $debugStmt->execute();
            $debugStmt->close();
        } catch (Exception $debugError) {
            error_log("Debug logging failed: " . $debugError->getMessage());
        }

        header("Location: register.php?error=dberror&details=" . urlencode($e->getMessage()));
        exit();
    }
}

?>