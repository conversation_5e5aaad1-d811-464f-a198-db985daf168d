<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Redirect if already logged in as organizer
if (isset($_SESSION['organizer_id'])) {
    header("Location: organizer_dashboard.php");
    exit();
}

$error = '';
$message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = "Please enter both email and password.";
    } else {
        try {
            // Check organizer credentials
            $stmt = $pdo->prepare("SELECT OrganizerId, OrganizerName, Email, PasswordHash, IsApproved FROM EventOrganizers WHERE Email = ?");
            $stmt->execute([$email]);
            $organizer = $stmt->fetch();
            
            if ($organizer && password_verify($password, $organizer['PasswordHash'])) {
                if ($organizer['IsApproved'] == 1) {
                    // Set organizer session
                    $_SESSION['organizer_id'] = $organizer['OrganizerId'];
                    $_SESSION['organizer_name'] = $organizer['OrganizerName'];
                    $_SESSION['organizer_email'] = $organizer['Email'];
                    $_SESSION['organizer_login_time'] = time();
                    
                    // Regenerate session ID for security
                    session_regenerate_id(true);
                    
                    // Log the organizer login
                    logSecurityEvent('organizer_login', ['organizer_id' => $organizer['OrganizerId']]);
                    
                    // Redirect to organizer dashboard
                    header("Location: organizer_dashboard.php");
                    exit();
                } else {
                    $error = "Your organizer account is pending approval. Please contact admin.";
                }
            } else {
                $error = "Invalid email or password.";
                logSecurityEvent('organizer_login_failed', ['email' => $email]);
            }
        } catch (Exception $e) {
            $error = "Login failed. Please try again.";
            error_log("Organizer login error: " . $e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organizer Login - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            margin: 0 10px;
        }
        .links a:hover {
            text-decoration: underline;
        }
        .info-box {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .info-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-calendar-alt"></i> Organizer Login</h1>
            <p>Access your event management dashboard</p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Development credentials info -->
        <div class="info-box">
            <h4><i class="fas fa-info-circle"></i> Demo Organizer Credentials</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> password</p>
            <p><em>Use these credentials to test the organizer features</em></p>
        </div>

        <form method="POST">
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input type="email" id="email" name="email" required 
                       value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                       placeholder="Enter your email">
            </div>

            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> Password
                </label>
                <input type="password" id="password" name="password" required
                       placeholder="Enter your password">
            </div>

            <button type="submit" class="btn">
                <i class="fas fa-sign-in-alt"></i> Login to Dashboard
            </button>
        </form>

        <div class="links">
            <a href="Finalll_updated.php">
                <i class="fas fa-home"></i> Back to Main Site
            </a>
            <a href="login.php">
                <i class="fas fa-user"></i> User Login
            </a>
            <a href="admin_login.php">
                <i class="fas fa-shield-alt"></i> Admin Login
            </a>
        </div>

        <div class="info-box" style="margin-top: 20px;">
            <h4>New Organizer?</h4>
            <p>Contact the admin to create your organizer account. Admins can add new organizers through the <strong>Admin Dashboard → Organizer Management</strong> section.</p>
            <p><small>Once your account is created and approved, you can login here to manage your events.</small></p>
        </div>
    </div>
</body>
</html>
