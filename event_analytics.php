<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if organizer is logged in
if (!isset($_SESSION['organizer_id'])) {
    header("Location: organizer_login.php");
    exit();
}

$organizerId = $_SESSION['organizer_id'];
$eventId = $_GET['id'] ?? '';

if (empty($eventId)) {
    header("Location: manage_events.php");
    exit();
}

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM Events WHERE EventId = ? AND OrganizerId = ?");
    $stmt->execute([$eventId, $organizerId]);
    $event = $stmt->fetch();
    
    if (!$event) {
        header("Location: manage_events.php");
        exit();
    }
    
    // Get comprehensive analytics
    $analyticsStmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT b.BookingId) as total_bookings,
            COUNT(DISTINCT CASE WHEN b.PaymentStatus = 'Completed' THEN b.BookingId END) as completed_bookings,
            COUNT(DISTINCT CASE WHEN b.PaymentStatus = 'Pending' THEN b.BookingId END) as pending_bookings,
            COUNT(DISTINCT CASE WHEN b.PaymentStatus = 'Failed' THEN b.BookingId END) as failed_bookings,
            COALESCE(SUM(CASE WHEN b.PaymentStatus = 'Completed' THEN b.TotalAmount END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN b.PaymentStatus = 'Pending' THEN b.TotalAmount END), 0) as pending_revenue,
            COALESCE(AVG(CASE WHEN b.PaymentStatus = 'Completed' THEN b.TotalAmount END), 0) as avg_booking_value,
            COUNT(DISTINCT b.UserId) as unique_customers
        FROM Events e
        LEFT JOIN Bookings b ON e.EventId = b.EventId
        WHERE e.EventId = ?
    ");
    $analyticsStmt->execute([$eventId]);
    $analytics = $analyticsStmt->fetch();
    
    // Get payment method breakdown
    $paymentStmt = $pdo->prepare("
        SELECT 
            p.Method,
            COUNT(*) as count,
            SUM(p.Amount) as total_amount
        FROM Payments p
        JOIN Bookings b ON p.BookingId = b.BookingId
        WHERE b.EventId = ? AND p.Status = 'Completed'
        GROUP BY p.Method
        ORDER BY total_amount DESC
    ");
    $paymentStmt->execute([$eventId]);
    $paymentMethods = $paymentStmt->fetchAll();
    
    // Get daily booking trends (last 30 days)
    $trendsStmt = $pdo->prepare("
        SELECT 
            DATE(b.BookingDate) as booking_date,
            COUNT(*) as bookings_count,
            SUM(b.TotalAmount) as daily_revenue
        FROM Bookings b
        WHERE b.EventId = ? 
        AND b.BookingDate >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(b.BookingDate)
        ORDER BY booking_date DESC
        LIMIT 30
    ");
    $trendsStmt->execute([$eventId]);
    $dailyTrends = $trendsStmt->fetchAll();
    
    // Get ticket type breakdown
    $ticketStmt = $pdo->prepare("
        SELECT 
            t.TicketType,
            t.AvailableTickets,
            t.Price,
            COUNT(b.BookingId) as bookings_count,
            SUM(b.TotalAmount) as revenue
        FROM Tickets t
        LEFT JOIN Bookings b ON t.EventId = b.EventId
        WHERE t.EventId = ?
        GROUP BY t.TicketType, t.AvailableTickets, t.Price
        ORDER BY t.Price DESC
    ");
    $ticketStmt->execute([$eventId]);
    $ticketTypes = $ticketStmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Error loading analytics: " . $e->getMessage();
}

// Calculate conversion rate
$conversionRate = $analytics['total_bookings'] > 0 ? 
    ($analytics['completed_bookings'] / $analytics['total_bookings']) * 100 : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Analytics - <?= htmlspecialchars($event['Title'] ?? 'Event') ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header-content { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { display: flex; align-items: center; gap: 10px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; font-weight: 600; transition: all 0.3s ease; }
        .btn-secondary { background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); }
        .btn-secondary:hover { background: rgba(255,255,255,0.3); }
        .container { max-width: 1200px; margin: 0 auto; padding: 30px 20px; }
        .event-title { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 20px; margin-bottom: 30px; text-align: center; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 25px; text-align: center; }
        .stat-card .icon { font-size: 2.5em; margin-bottom: 15px; }
        .stat-card .value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-card .label { color: #666; font-size: 14px; }
        .chart-section { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 30px; }
        .chart-container { position: relative; height: 300px; margin-top: 20px; }
        .table-section { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 30px; }
        .analytics-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .analytics-table th, .analytics-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e9ecef; }
        .analytics-table th { background: #f8f9fa; font-weight: 600; }
        .progress-bar { background: #e9ecef; border-radius: 10px; height: 8px; margin-top: 5px; }
        .progress-fill { background: #667eea; height: 100%; border-radius: 10px; transition: width 0.3s ease; }
        .metric-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric-item { text-align: center; }
        .metric-value { font-size: 1.5em; font-weight: bold; color: #667eea; }
        .metric-label { color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-chart-line"></i> Event Analytics</h1>
            <div style="display: flex; gap: 10px;">
                <a href="event_details.php?id=<?= $eventId ?>" class="btn btn-secondary">
                    <i class="fas fa-eye"></i> View Details
                </a>
                <a href="manage_events.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Events
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Event Title -->
        <div class="event-title">
            <h2><?= htmlspecialchars($event['Title']) ?></h2>
            <p style="color: #666; margin-top: 10px;">
                <?= date('M d, Y H:i', strtotime($event['EventDate'])) ?> • <?= htmlspecialchars($event['Place']) ?>
            </p>
        </div>

        <!-- Key Metrics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon" style="color: #28a745;">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="value" style="color: #28a745;"><?= number_format($analytics['total_bookings']) ?></div>
                <div class="label">Total Bookings</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #17a2b8;">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="value" style="color: #17a2b8;"><?= number_format($analytics['total_revenue'], 2) ?></div>
                <div class="label">Total Revenue (ETB)</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #ffc107;">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="value" style="color: #ffc107;"><?= number_format($conversionRate, 1) ?>%</div>
                <div class="label">Conversion Rate</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #dc3545;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="value" style="color: #dc3545;"><?= number_format($analytics['unique_customers']) ?></div>
                <div class="label">Unique Customers</div>
            </div>
        </div>

        <!-- Booking Status Breakdown -->
        <div class="chart-section">
            <h3><i class="fas fa-chart-pie"></i> Booking Status Breakdown</h3>
            <div class="metric-row">
                <div class="metric-item">
                    <div class="metric-value" style="color: #28a745;"><?= $analytics['completed_bookings'] ?></div>
                    <div class="metric-label">Completed</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" style="color: #ffc107;"><?= $analytics['pending_bookings'] ?></div>
                    <div class="metric-label">Pending</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" style="color: #dc3545;"><?= $analytics['failed_bookings'] ?></div>
                    <div class="metric-label">Failed</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" style="color: #17a2b8;"><?= number_format($analytics['avg_booking_value'], 2) ?></div>
                    <div class="metric-label">Avg. Booking Value (ETB)</div>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <?php if (count($paymentMethods) > 0): ?>
        <div class="table-section">
            <h3><i class="fas fa-credit-card"></i> Payment Methods</h3>
            <table class="analytics-table">
                <thead>
                    <tr>
                        <th>Payment Method</th>
                        <th>Transactions</th>
                        <th>Total Amount (ETB)</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $totalPaymentAmount = array_sum(array_column($paymentMethods, 'total_amount'));
                    foreach ($paymentMethods as $method): 
                        $percentage = $totalPaymentAmount > 0 ? ($method['total_amount'] / $totalPaymentAmount) * 100 : 0;
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($method['Method']) ?></td>
                        <td><?= number_format($method['count']) ?></td>
                        <td><?= number_format($method['total_amount'], 2) ?></td>
                        <td>
                            <?= number_format($percentage, 1) ?>%
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: <?= $percentage ?>%;"></div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- Ticket Types Performance -->
        <?php if (count($ticketTypes) > 0): ?>
        <div class="table-section">
            <h3><i class="fas fa-tags"></i> Ticket Types Performance</h3>
            <table class="analytics-table">
                <thead>
                    <tr>
                        <th>Ticket Type</th>
                        <th>Price (ETB)</th>
                        <th>Available</th>
                        <th>Bookings</th>
                        <th>Revenue (ETB)</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($ticketTypes as $ticket): ?>
                    <tr>
                        <td><?= htmlspecialchars($ticket['TicketType']) ?></td>
                        <td><?= number_format($ticket['Price'], 2) ?></td>
                        <td><?= number_format($ticket['AvailableTickets']) ?></td>
                        <td><?= number_format($ticket['bookings_count']) ?></td>
                        <td><?= number_format($ticket['revenue'], 2) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <!-- Daily Trends -->
        <?php if (count($dailyTrends) > 0): ?>
        <div class="table-section">
            <h3><i class="fas fa-calendar-alt"></i> Recent Daily Trends</h3>
            <table class="analytics-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Bookings</th>
                        <th>Revenue (ETB)</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (array_slice($dailyTrends, 0, 10) as $trend): ?>
                    <tr>
                        <td><?= date('M d, Y', strtotime($trend['booking_date'])) ?></td>
                        <td><?= number_format($trend['bookings_count']) ?></td>
                        <td><?= number_format($trend['daily_revenue'], 2) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
