<?php
/**
 * Fix Support Tickets Table Structure
 * Adds missing columns for feedback system compatibility
 */

require_once 'db_connect.php';

$message = '';
$error = '';
$updates = [];

try {
    // Check current table structure
    $stmt = $pdo->query("DESCRIBE SupportTickets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $updates[] = "Current columns: " . implode(', ', $columns);
    
    // Check if Name column exists
    if (!in_array('Name', $columns)) {
        $pdo->exec("ALTER TABLE SupportTickets ADD COLUMN Name VARCHAR(200) NOT NULL DEFAULT 'Anonymous' AFTER UserId");
        $updates[] = "✅ Added 'Name' column";
    } else {
        $updates[] = "✅ 'Name' column already exists";
    }
    
    // Check if Subject column exists
    if (!in_array('Subject', $columns)) {
        $pdo->exec("ALTER TABLE SupportTickets ADD COLUMN Subject VARCHAR(300) NOT NULL DEFAULT 'No Subject' AFTER Category");
        $updates[] = "✅ Added 'Subject' column";
    } else {
        $updates[] = "✅ 'Subject' column already exists";
    }
    
    // Check if Priority column exists
    if (!in_array('Priority', $columns)) {
        $pdo->exec("ALTER TABLE SupportTickets ADD COLUMN Priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium' AFTER Status");
        $updates[] = "✅ Added 'Priority' column";
    } else {
        $updates[] = "✅ 'Priority' column already exists";
    }
    
    // Check if AssignedTo column exists
    if (!in_array('AssignedTo', $columns)) {
        $pdo->exec("ALTER TABLE SupportTickets ADD COLUMN AssignedTo VARCHAR(50) NULL AFTER Priority");
        $updates[] = "✅ Added 'AssignedTo' column";
    } else {
        $updates[] = "✅ 'AssignedTo' column already exists";
    }
    
    // Check if UpdatedDate column exists
    if (!in_array('UpdatedDate', $columns)) {
        $pdo->exec("ALTER TABLE SupportTickets ADD COLUMN UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER CreatedDate");
        $updates[] = "✅ Added 'UpdatedDate' column";
    } else {
        $updates[] = "✅ 'UpdatedDate' column already exists";
    }
    
    // Update Category enum to include 'feedback' if not present
    $stmt = $pdo->query("SHOW COLUMNS FROM SupportTickets WHERE Field = 'Category'");
    $categoryInfo = $stmt->fetch();
    if ($categoryInfo && strpos($categoryInfo['Type'], 'feedback') === false) {
        $pdo->exec("ALTER TABLE SupportTickets MODIFY COLUMN Category ENUM('general', 'booking', 'payment', 'account', 'technical', 'refund', 'feedback') NOT NULL");
        $updates[] = "✅ Updated 'Category' enum to include 'feedback'";
    } else {
        $updates[] = "✅ 'Category' enum already includes 'feedback'";
    }
    
    // Get final table structure
    $stmt = $pdo->query("DESCRIBE SupportTickets");
    $finalColumns = $stmt->fetchAll();
    
    $message = "Support table structure updated successfully!";
    
} catch (Exception $e) {
    $error = "Error updating table structure: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Support Table - Database Update</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; color: #333; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .btn { padding: 15px 30px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: 600; text-decoration: none; display: inline-block; }
        .btn:hover { background: #5a6fd8; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .update-list { list-style: none; padding: 0; }
        .update-list li { padding: 10px; margin: 5px 0; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #667eea; }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #e9ecef; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .code { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; margin: 15px 0; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Support Table Structure Fix</h1>
            <p>Update database table for feedback system compatibility</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <strong>✅ Success!</strong> <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <strong>❌ Error!</strong> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <h2>📋 Update Summary</h2>
            <?php if (!empty($updates)): ?>
                <ul class="update-list">
                    <?php foreach ($updates as $update): ?>
                        <li><?= htmlspecialchars($update) ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>

        <?php if (isset($finalColumns) && !empty($finalColumns)): ?>
        <div class="card">
            <h2>📊 Final Table Structure</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Column</th>
                        <th>Type</th>
                        <th>Null</th>
                        <th>Key</th>
                        <th>Default</th>
                        <th>Extra</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($finalColumns as $column): ?>
                    <tr>
                        <td><strong><?= htmlspecialchars($column['Field']) ?></strong></td>
                        <td><?= htmlspecialchars($column['Type']) ?></td>
                        <td><?= htmlspecialchars($column['Null']) ?></td>
                        <td><?= htmlspecialchars($column['Key']) ?></td>
                        <td><?= htmlspecialchars($column['Default'] ?? 'NULL') ?></td>
                        <td><?= htmlspecialchars($column['Extra']) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <div class="card">
            <h2>🚀 Next Steps</h2>
            <p>After running this fix, your feedback system should work properly. You can now:</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li>✅ Access the feedback form: <a href="feedback.php">feedback.php</a></li>
                <li>✅ Manage feedback as admin: <a href="admin_feedback.php">admin_feedback.php</a></li>
                <li>✅ Test the complete feedback workflow</li>
            </ul>
            
            <div style="margin-top: 20px;">
                <a href="feedback.php" class="btn">Test Feedback Form</a>
                <a href="admin_feedback.php" class="btn" style="margin-left: 10px;">Admin Dashboard</a>
            </div>
        </div>

        <div class="card">
            <h2>🔍 Manual SQL (if needed)</h2>
            <p>If the automatic update fails, you can run these SQL commands manually:</p>
            <div class="code">
-- Add missing columns to SupportTickets table
ALTER TABLE SupportTickets ADD COLUMN IF NOT EXISTS Name VARCHAR(200) NOT NULL DEFAULT 'Anonymous' AFTER UserId;
ALTER TABLE SupportTickets ADD COLUMN IF NOT EXISTS Subject VARCHAR(300) NOT NULL DEFAULT 'No Subject' AFTER Category;
ALTER TABLE SupportTickets ADD COLUMN IF NOT EXISTS Priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium' AFTER Status;
ALTER TABLE SupportTickets ADD COLUMN IF NOT EXISTS AssignedTo VARCHAR(50) NULL AFTER Priority;
ALTER TABLE SupportTickets ADD COLUMN IF NOT EXISTS UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER CreatedDate;

-- Update Category enum
ALTER TABLE SupportTickets MODIFY COLUMN Category ENUM('general', 'booking', 'payment', 'account', 'technical', 'refund', 'feedback') NOT NULL;
            </div>
        </div>

        <div class="card">
            <h2>⚠️ Important Notes</h2>
            <div class="alert alert-error">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Backup First:</strong> Always backup your database before making structural changes</li>
                    <li><strong>Test Environment:</strong> Test these changes in a development environment first</li>
                    <li><strong>Existing Data:</strong> This update preserves all existing data</li>
                    <li><strong>Default Values:</strong> New columns have safe default values</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
