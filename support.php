<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

$message = '';
$error = '';

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        $name = trim($_POST['name'] ?? '');
        $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
        $subject = trim($_POST['subject'] ?? '');
        $messageText = trim($_POST['message'] ?? '');
        $category = $_POST['category'] ?? 'general';

        if (empty($name) || empty($email) || empty($subject) || empty($messageText)) {
            $error = "Please fill in all required fields.";
        } else {
            try {
                // Create support ticket
                $ticketId = 'SUP' . substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 6);
                
                $stmt = $pdo->prepare("
                    INSERT INTO SupportTickets (TicketId, UserId, Name, Email, Subject, Message, Category, Status, CreatedDate) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'Open', NOW())
                ");
                
                $userId = $_SESSION['user_id'] ?? null;
                $stmt->execute([$ticketId, $userId, $name, $email, $subject, $messageText, $category]);
                
                $message = "Your support ticket has been submitted successfully. Ticket ID: " . $ticketId;
                
                // Log support ticket creation
                logSecurityEvent('support_ticket_created', [
                    'ticket_id' => $ticketId,
                    'user_id' => $userId,
                    'category' => $category
                ]);
                
            } catch (Exception $e) {
                $error = "Failed to submit support ticket. Please try again.";
                logSecurityEvent('support_ticket_error', ['error' => $e->getMessage()]);
            }
        }
    }
}

// Get FAQs
try {
    $faqStmt = $pdo->prepare("SELECT * FROM FAQs WHERE IsActive = 1 ORDER BY SortOrder, CreatedDate DESC");
    $faqStmt->execute();
    $faqs = $faqStmt->fetchAll();
} catch (Exception $e) {
    $faqs = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Support - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .support-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .support-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        .support-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        .support-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .support-card .icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #667eea;
        }
        .support-card h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .support-card p {
            color: #666;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        .contact-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .faq-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .faq-item {
            border-bottom: 1px solid #eee;
            padding: 20px 0;
        }
        .faq-item:last-child {
            border-bottom: none;
        }
        .faq-question {
            font-weight: 600;
            color: #333;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .faq-question:hover {
            color: #667eea;
        }
        .faq-answer {
            margin-top: 15px;
            color: #666;
            line-height: 1.6;
            display: none;
        }
        .faq-answer.show {
            display: block;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .quick-link {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .quick-link:hover {
            transform: translateY(-5px);
        }
        .quick-link .icon {
            font-size: 32px;
            color: #667eea;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .support-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img alt="Addis Ababa Stadium Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
        </nav>
    </header>

    <div class="support-container">
        <div class="support-header">
            <h1><i class="fas fa-headset"></i> Customer Support</h1>
            <p>We're here to help! Get assistance with your bookings, account, or any questions you may have.</p>
        </div>

        <!-- Quick Links -->
        <div class="quick-links">
            <div class="quick-link">
                <div class="icon"><i class="fas fa-ticket-alt"></i></div>
                <h4>My Tickets</h4>
                <p>View and manage your bookings</p>
                <a href="tickets.php" class="btn btn-outline">View Tickets</a>
            </div>
            <div class="quick-link">
                <div class="icon"><i class="fas fa-user"></i></div>
                <h4>My Account</h4>
                <p>Update profile and settings</p>
                <a href="profile.php" class="btn btn-outline">My Profile</a>
            </div>
            <div class="quick-link">
                <div class="icon"><i class="fas fa-calendar"></i></div>
                <h4>Browse Events</h4>
                <p>Find upcoming events</p>
                <a href="Finalll_updated.php" class="btn btn-outline">Browse Events</a>
            </div>
            <div class="quick-link">
                <div class="icon"><i class="fas fa-phone"></i></div>
                <h4>Emergency Contact</h4>
                <p>Call us: +251-11-XXX-XXXX</p>
                <a href="tel:+251-11-XXX-XXXX" class="btn btn-outline">Call Now</a>
            </div>
        </div>

        <!-- Support Options -->
        <div class="support-grid">
            <div class="support-card">
                <div class="icon"><i class="fas fa-envelope"></i></div>
                <h3>Email Support</h3>
                <p>Send us a detailed message and we'll get back to you within 24 hours.</p>
                <a href="#contact-form" class="btn">Send Message</a>
            </div>
            <div class="support-card">
                <div class="icon"><i class="fas fa-comments"></i></div>
                <h3>Live Chat</h3>
                <p>Chat with our support team in real-time during business hours.</p>
                <button onclick="startLiveChat()" class="btn">Start Chat</button>
            </div>
        </div>

        <!-- Contact Form -->
        <div class="contact-form" id="contact-form">
            <h2><i class="fas fa-paper-plane"></i> Send us a Message</h2>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                
                <div class="form-group">
                    <label for="category">Category</label>
                    <select name="category" id="category" required>
                        <option value="general">General Inquiry</option>
                        <option value="booking">Booking Issues</option>
                        <option value="payment">Payment Problems</option>
                        <option value="account">Account Issues</option>
                        <option value="technical">Technical Support</option>
                        <option value="refund">Refund Request</option>
                        <option value="feedback">Feedback</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="name">Full Name</label>
                    <input type="text" id="name" name="name" 
                           value="<?= isset($_SESSION['first_name']) ? htmlspecialchars($_SESSION['first_name']) : '' ?>" 
                           required>
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" 
                           value="<?= isset($_SESSION['user_email']) ? htmlspecialchars($_SESSION['user_email']) : '' ?>" 
                           required>
                </div>

                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" id="subject" name="subject" 
                           placeholder="Brief description of your issue" required>
                </div>

                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" 
                              placeholder="Please provide detailed information about your issue..." required></textarea>
                </div>

                <button type="submit" class="btn">
                    <i class="fas fa-paper-plane"></i> Send Message
                </button>
            </form>
        </div>

        <!-- FAQ Section -->
        <div class="faq-section">
            <h2><i class="fas fa-question-circle"></i> Frequently Asked Questions</h2>
            
            <?php if (empty($faqs)): ?>
                <!-- Default FAQs if none in database -->
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        How do I book tickets?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        To book tickets, browse our events, select your preferred event, choose your seats, and proceed to checkout. You'll need to create an account and verify your email address.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        What payment methods do you accept?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        We accept various payment methods including Telebirr, CBE Birr, and other local payment options. All payments are processed securely.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        Can I cancel or refund my tickets?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        Refund policies vary by event. Generally, tickets can be refunded up to 48 hours before the event. Please contact support for specific refund requests.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        How do I access my tickets?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        After successful payment, you can access your tickets from your account dashboard. You can also download or print your tickets for entry.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        What if I forgot my password?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        Use the "Forgot Password" link on the login page to reset your password. You'll receive an email with instructions to create a new password.
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($faqs as $faq): ?>
                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFAQ(this)">
                            <?= htmlspecialchars($faq['Question']) ?>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <?= nl2br(htmlspecialchars($faq['Answer'])) ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function toggleFAQ(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('i');
            
            answer.classList.toggle('show');
            
            if (answer.classList.contains('show')) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        }

        function startLiveChat() {
            // Simulate live chat (you can integrate with actual chat service)
            alert('Live chat feature coming soon! For immediate assistance, please use the contact form or call us at +251-11-XXX-XXXX');
        }

        // Auto-scroll to contact form if there's an error or message
        <?php if ($message || $error): ?>
            document.getElementById('contact-form').scrollIntoView({ behavior: 'smooth' });
        <?php endif; ?>
    </script>
</body>
</html>
