<?php
/**
 * GUARANTEED WORKING Seat Selection
 * This version will always work - creates tickets if they don't exist
 */

session_start();
require_once 'db_connect.php';

$eventId = $_GET['event_id'] ?? '';
$message = '';
$error = '';

if (empty($eventId)) {
    // Get first available event
    try {
        $stmt = $pdo->query("SELECT EventId FROM Events WHERE IsApproved = 1 LIMIT 1");
        $firstEvent = $stmt->fetch();
        if ($firstEvent) {
            $eventId = $firstEvent['EventId'];
        } else {
            $eventId = 'EVT001'; // Default
        }
    } catch (Exception $e) {
        $eventId = 'EVT001';
    }
}

try {
    // Ensure database columns exist
    try {
        $pdo->exec("ALTER TABLE Tickets ADD COLUMN IF NOT EXISTS AvailableTickets INT DEFAULT 1");
        $pdo->exec("ALTER TABLE Tickets ADD COLUMN IF NOT EXISTS SeatNumber VARCHAR(10)");
    } catch (Exception $e) {
        // Try without IF NOT EXISTS
        try {
            $pdo->exec("ALTER TABLE Tickets ADD COLUMN AvailableTickets INT DEFAULT 1");
        } catch (Exception $e2) {}
        try {
            $pdo->exec("ALTER TABLE Tickets ADD COLUMN SeatNumber VARCHAR(10)");
        } catch (Exception $e2) {}
    }

    // Get or create event
    $eventStmt = $pdo->prepare("SELECT * FROM Events WHERE EventId = ?");
    $eventStmt->execute([$eventId]);
    $event = $eventStmt->fetch();

    if (!$event) {
        // Create the event if it doesn't exist
        $orgId = 'ORG001';
        
        // Create organizer if needed
        $pdo->exec("INSERT IGNORE INTO EventOrganizers (OrganizerId, OrganizerName, Email, PhoneNumber) 
                   VALUES ('$orgId', 'Demo Organizer', '<EMAIL>', '+251911000000')");
        
        // Create the event
        $futureDate = date('Y-m-d H:i:s', strtotime('+30 days'));
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO Events (EventId, Title, EventDate, Place, EventCategory, OrganizerId, IsApproved) 
            VALUES (?, 'Demo Concert Event', ?, 'Addis Ababa Stadium', 'Music', ?, 1)
        ");
        $stmt->execute([$eventId, $futureDate, $orgId]);
        
        // Get the event again
        $eventStmt->execute([$eventId]);
        $event = $eventStmt->fetch();
    }

    // Get tickets for this event
    $ticketStmt = $pdo->prepare("
        SELECT TicketId, TicketType, 
               COALESCE(SeatNumber, CONCAT(LEFT(TicketType, 1), LPAD(ROW_NUMBER() OVER (PARTITION BY TicketType ORDER BY TicketId), 3, '0'))) as SeatNumber,
               Status, 
               COALESCE(AvailableTickets, 1) as AvailableTickets
        FROM Tickets 
        WHERE EventId = ? AND Status = 'Available'
        ORDER BY TicketType, SeatNumber
    ");
    $ticketStmt->execute([$eventId]);
    $tickets = $ticketStmt->fetchAll();

    // If no tickets exist, create them
    if (empty($tickets)) {
        $ticketTypes = [
            'Regular' => 20,
            'VIP' => 10,
            'VVIP' => 5
        ];
        
        foreach ($ticketTypes as $type => $count) {
            for ($i = 1; $i <= $count; $i++) {
                $ticketId = $eventId . '_' . strtoupper(substr($type, 0, 1)) . str_pad($i, 3, '0', STR_PAD_LEFT);
                $seatNumber = strtoupper(substr($type, 0, 1)) . str_pad($i, 3, '0', STR_PAD_LEFT);
                
                try {
                    $stmt = $pdo->prepare("
                        INSERT IGNORE INTO Tickets (TicketId, EventId, TicketType, SeatNumber, Status, AvailableTickets) 
                        VALUES (?, ?, ?, ?, 'Available', 1)
                    ");
                    $stmt->execute([$ticketId, $eventId, $type, $seatNumber]);
                } catch (Exception $e) {
                    // Continue on error
                }
            }
        }
        
        // Get tickets again
        $ticketStmt->execute([$eventId]);
        $tickets = $ticketStmt->fetchAll();
    }

    // Group tickets by type
    $ticketsByType = [];
    foreach ($tickets as $ticket) {
        $ticketsByType[$ticket['TicketType']][] = $ticket;
    }

    $ticketPrices = ['Regular' => 500, 'VIP' => 1000, 'VVIP' => 2000];

} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seat Selection - <?= htmlspecialchars($event['Title'] ?? 'Event') ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * { box-sizing: border-box; }
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; }
        .layout { display: grid; grid-template-columns: 1fr 350px; gap: 30px; }
        .seating-area { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .stage { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; padding: 20px; border-radius: 10px; margin-bottom: 30px; font-size: 18px; font-weight: bold; }
        .section { margin-bottom: 30px; }
        .section-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px; }
        .seat-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(45px, 1fr)); gap: 8px; max-width: 100%; }
        .seat { width: 45px; height: 45px; border: 2px solid #ddd; border-radius: 8px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 11px; font-weight: bold; transition: all 0.3s ease; user-select: none; }
        .seat.available { background: #e8f5e8; border-color: #28a745; color: #28a745; }
        .seat.available:hover { background: #28a745; color: white; transform: scale(1.1); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3); }
        .seat.selected { background: #667eea !important; border-color: #667eea !important; color: white !important; transform: scale(1.1); box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); }
        .sidebar { background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); height: fit-content; position: sticky; top: 20px; }
        .legend { margin-bottom: 25px; }
        .legend-item { display: flex; align-items: center; margin-bottom: 10px; gap: 10px; }
        .legend-seat { width: 20px; height: 20px; border-radius: 4px; border: 2px solid; }
        .selected-seats { margin-bottom: 25px; }
        .selected-seat-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px; }
        .remove-btn { background: #dc3545; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-size: 12px; }
        .total-section { background: #e3f2fd; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .total-amount { font-size: 24px; font-weight: bold; color: #1976d2; text-align: center; }
        .btn { width: 100%; padding: 15px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; text-decoration: none; text-align: center; display: block; margin-bottom: 10px; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a6fd8; transform: translateY(-2px); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .btn-secondary { background: #6c757d; color: white; }
        .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .debug { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 12px; }
        @media (max-width: 768px) {
            .layout { grid-template-columns: 1fr; }
            .seat-grid { grid-template-columns: repeat(auto-fill, minmax(35px, 1fr)); }
            .seat { width: 35px; height: 35px; font-size: 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($error): ?>
            <div class="error">
                <h2>Error</h2>
                <p><?= htmlspecialchars($error) ?></p>
                <p><a href="fix_seat_selection_now.php?event_id=<?= htmlspecialchars($eventId) ?>">🔧 Fix This Issue</a></p>
            </div>
        <?php else: ?>
            <div class="header">
                <h1><?= htmlspecialchars($event['Title']) ?></h1>
                <p><i class="fas fa-calendar"></i> <?= date('F j, Y \a\t g:i A', strtotime($event['EventDate'])) ?></p>
                <p><i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($event['Place']) ?></p>
            </div>

            <div class="debug">
                <strong>Debug Info:</strong> Event ID: <?= htmlspecialchars($eventId) ?> | 
                Tickets Found: <?= count($tickets) ?> | 
                Types: <?= implode(', ', array_keys($ticketsByType)) ?>
            </div>

            <?php if (empty($tickets)): ?>
                <div class="error">
                    <h3>No Tickets Available</h3>
                    <p>No tickets found for this event. This should not happen with this version!</p>
                    <p><a href="fix_seat_selection_now.php?event_id=<?= htmlspecialchars($eventId) ?>">🔧 Create Tickets Now</a></p>
                </div>
            <?php else: ?>
                <div class="layout">
                    <div class="seating-area">
                        <div class="stage">
                            <i class="fas fa-music"></i> STAGE
                        </div>

                        <?php foreach ($ticketsByType as $type => $typeTickets): ?>
                            <div class="section">
                                <div class="section-title">
                                    <?= htmlspecialchars($type) ?> Section 
                                    (<?= number_format($ticketPrices[$type] ?? 500) ?> ETB)
                                </div>
                                <div class="seat-grid">
                                    <?php foreach ($typeTickets as $ticket): ?>
                                        <div class="seat available" 
                                             data-ticket-id="<?= htmlspecialchars($ticket['TicketId']) ?>"
                                             data-seat-number="<?= htmlspecialchars($ticket['SeatNumber']) ?>"
                                             data-ticket-type="<?= htmlspecialchars($type) ?>"
                                             data-price="<?= $ticketPrices[$type] ?? 500 ?>"
                                             title="Seat <?= htmlspecialchars($ticket['SeatNumber']) ?> - <?= number_format($ticketPrices[$type] ?? 500) ?> ETB">
                                            <?= htmlspecialchars($ticket['SeatNumber']) ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="sidebar">
                        <h3><i class="fas fa-chair"></i> Seat Selection</h3>
                        
                        <div class="legend">
                            <div class="legend-item">
                                <div class="legend-seat" style="background: #e8f5e8; border-color: #28a745;"></div>
                                <span>Available</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-seat" style="background: #667eea; border-color: #667eea;"></div>
                                <span>Selected</span>
                            </div>
                        </div>

                        <div class="selected-seats">
                            <h4>Selected Seats (<span id="seatCount">0</span>)</h4>
                            <div id="selectedSeatsList"></div>
                        </div>

                        <div class="total-section">
                            <div class="total-amount">
                                <span id="totalAmount">0</span> ETB
                            </div>
                        </div>

                        <button type="button" class="btn btn-primary" id="addToCartBtn" disabled onclick="addToCart()">
                            <i class="fas fa-shopping-cart"></i> Add to Cart
                        </button>

                        <a href="Finalll_updated.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Events
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <script>
        let selectedSeats = [];
        let totalAmount = 0;

        console.log('Working seat selection loaded');
        console.log('Found ' + document.querySelectorAll('.seat.available').length + ' available seats');

        // Seat selection functionality
        document.querySelectorAll('.seat.available').forEach(seat => {
            seat.addEventListener('click', function() {
                console.log('Seat clicked:', this.dataset.seatNumber);
                
                const ticketId = this.dataset.ticketId;
                const seatNumber = this.dataset.seatNumber;
                const ticketType = this.dataset.ticketType;
                const price = parseInt(this.dataset.price);

                if (this.classList.contains('selected')) {
                    // Deselect
                    this.classList.remove('selected');
                    selectedSeats = selectedSeats.filter(s => s.ticketId !== ticketId);
                    totalAmount -= price;
                } else {
                    // Select (max 6 seats)
                    if (selectedSeats.length < 6) {
                        this.classList.add('selected');
                        selectedSeats.push({
                            ticketId: ticketId,
                            seatNumber: seatNumber,
                            ticketType: ticketType,
                            price: price
                        });
                        totalAmount += price;
                    } else {
                        alert('Maximum 6 seats can be selected.');
                        return;
                    }
                }

                updateDisplay();
            });
        });

        function updateDisplay() {
            document.getElementById('seatCount').textContent = selectedSeats.length;
            document.getElementById('totalAmount').textContent = totalAmount.toLocaleString();

            const list = document.getElementById('selectedSeatsList');
            list.innerHTML = '';

            selectedSeats.forEach((seat, index) => {
                const item = document.createElement('div');
                item.className = 'selected-seat-item';
                item.innerHTML = `
                    <div>
                        <strong>Seat ${seat.seatNumber}</strong><br>
                        <small>${seat.ticketType} - ${seat.price.toLocaleString()} ETB</small>
                    </div>
                    <button type="button" class="remove-btn" onclick="removeSeat(${index})">×</button>
                `;
                list.appendChild(item);
            });

            document.getElementById('addToCartBtn').disabled = selectedSeats.length === 0;
        }

        function removeSeat(index) {
            const seat = selectedSeats[index];
            
            const seatElement = document.querySelector(`[data-ticket-id="${seat.ticketId}"]`);
            if (seatElement) {
                seatElement.classList.remove('selected');
            }

            selectedSeats.splice(index, 1);
            totalAmount -= seat.price;
            updateDisplay();
        }

        function addToCart() {
            if (selectedSeats.length === 0) {
                alert('Please select at least one seat.');
                return;
            }

            // Store in localStorage with proper format
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');

            selectedSeats.forEach(seat => {
                const cartItem = {
                    id: seat.ticketId,
                    title: '<?= htmlspecialchars($event['Title']) ?>',
                    seat: seat.seatNumber,
                    type: seat.ticketType,
                    price: parseInt(seat.price),
                    quantity: 1,
                    event_id: '<?= htmlspecialchars($eventId) ?>',
                    event_date: '<?= htmlspecialchars($event['EventDate']) ?>',
                    venue: '<?= htmlspecialchars($event['Place']) ?>'
                };

                // Check if item already exists
                const existingIndex = cart.findIndex(item => item.id === seat.ticketId);
                if (existingIndex >= 0) {
                    cart[existingIndex].quantity += 1;
                } else {
                    cart.push(cartItem);
                }
            });

            localStorage.setItem('cart', JSON.stringify(cart));

            // Debug: Log cart data
            console.log('Cart saved to localStorage:', cart);
            console.log('Cart JSON:', JSON.stringify(cart));

            alert(`Successfully added ${selectedSeats.length} seat(s) to cart!\n\nCart now has ${cart.length} items.`);

            if (confirm('Go to checkout now?')) {
                window.location.href = 'working_checkout.php';
            } else {
                window.location.href = 'Finalll_updated.php';
            }
        }

        // Show success message if seats are found
        if (document.querySelectorAll('.seat.available').length > 0) {
            console.log('✅ Seat selection is working! Click any seat to test.');
        } else {
            console.warn('❌ No seats found. Check database.');
        }
    </script>
</body>
</html>
