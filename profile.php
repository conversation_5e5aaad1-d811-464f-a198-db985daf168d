<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) || !$_SESSION['loggedin']) {
    header("Location: login.php");
    exit();
}

// Secure session
if (!secureSession()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        try {
            // Get and validate form data
            $firstName = sanitizeInput($_POST['firstName']);
            $lastName = sanitizeInput($_POST['lastName']);
            $email = sanitizeInput($_POST['email'], 'email');
            $phone = sanitizeInput($_POST['phone']);
            $subcity = sanitizeInput($_POST['subcity']);
            $houseNumber = sanitizeInput($_POST['houseNumber'], 'int');
            
            // Validate required fields
            if (!$firstName || !$lastName || !$email || !$phone) {
                throw new Exception("All required fields must be filled");
            }
            
            // Check if email is already taken by another user
            $emailCheck = $pdo->prepare("SELECT UserId FROM Users WHERE Email = ? AND UserId != ?");
            $emailCheck->execute([$email, $userId]);
            if ($emailCheck->fetch()) {
                throw new Exception("This email is already registered to another account");
            }
            
            // Update user profile
            $updateStmt = $pdo->prepare("
                UPDATE Users 
                SET FirstName = ?, LastName = ?, Email = ?, PhoneNumber = ?, SubCity = ?, HouseNumber = ?
                WHERE UserId = ?
            ");
            $updateStmt->execute([$firstName, $lastName, $email, $phone, $subcity, $houseNumber, $userId]);
            
            // Update session data
            $_SESSION['first_name'] = $firstName;
            $_SESSION['user_email'] = $email;
            
            $message = "Profile updated successfully!";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get current user data
try {
    $stmt = $pdo->prepare("SELECT * FROM Users WHERE UserId = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    // Get user's booking history (compatible with current schema)
    $bookingStmt = $pdo->prepare("
        SELECT b.*, t.EventId, e.Title as EventTitle, e.EventDate, e.Place, p.Amount, p.Status as PaymentStatus
        FROM Bookings b
        LEFT JOIN Tickets t ON b.TicketId = t.TicketId
        LEFT JOIN Events e ON t.EventId = e.EventId
        LEFT JOIN Payments p ON b.BookingId = p.BookingId
        WHERE b.UserId = ?
        ORDER BY b.BookingDate DESC
        LIMIT 10
    ");
    $bookingStmt->execute([$userId]);
    $bookings = $bookingStmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Error loading profile data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .profile-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .profile-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .profile-content { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        .profile-section { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #5a6fd8; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .booking-item { border: 1px solid #eee; padding: 15px; margin-bottom: 10px; border-radius: 5px; }
        .booking-status { padding: 5px 10px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        @media (max-width: 768px) { .profile-content { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img alt="Addis Ababa Stadium Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
            <ul class="nav-links">
                <li><a href="Finalll_updated.php">Home</a></li>
                <li><a href="profile.php" class="active">Profile</a></li>
                <li><a href="tickets.php">My Tickets</a></li>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>

    <div class="profile-container">
        <div class="profile-header">
            <h1><i class="fas fa-user-circle"></i> My Profile</h1>
            <p>Welcome back, <?= htmlspecialchars($user['FirstName'] ?? 'User') ?>!</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <div class="profile-content">
            <!-- Profile Information -->
            <div class="profile-section">
                <h2><i class="fas fa-edit"></i> Profile Information</h2>
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    
                    <div class="form-group">
                        <label for="firstName">First Name *</label>
                        <input type="text" id="firstName" name="firstName" value="<?= htmlspecialchars($user['FirstName'] ?? '') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="lastName">Last Name *</label>
                        <input type="text" id="lastName" name="lastName" value="<?= htmlspecialchars($user['LastName'] ?? '') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" value="<?= htmlspecialchars($user['Email'] ?? '') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone Number *</label>
                        <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($user['PhoneNumber'] ?? '') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="subcity">Sub City</label>
                        <input type="text" id="subcity" name="subcity" value="<?= htmlspecialchars($user['SubCity'] ?? '') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="houseNumber">House Number</label>
                        <input type="number" id="houseNumber" name="houseNumber" value="<?= htmlspecialchars($user['HouseNumber'] ?? '') ?>">
                    </div>
                    
                    <button type="submit" class="btn">
                        <i class="fas fa-save"></i> Update Profile
                    </button>
                </form>
            </div>

            <!-- Recent Bookings -->
            <div class="profile-section">
                <h2><i class="fas fa-ticket-alt"></i> Recent Bookings</h2>
                <?php if (empty($bookings)): ?>
                    <p>No bookings found. <a href="Finalll_updated.php">Browse events</a> to make your first booking!</p>
                <?php else: ?>
                    <?php foreach ($bookings as $booking): ?>
                        <div class="booking-item">
                            <h4><?= htmlspecialchars($booking['EventTitle'] ?? 'Unknown Event') ?></h4>
                            <p><i class="fas fa-calendar"></i> <?= date('M d, Y', strtotime($booking['EventDate'])) ?></p>
                            <p><i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($booking['Place'] ?? 'Addis Ababa Stadium') ?></p>
                            <p><i class="fas fa-money-bill"></i> <?= number_format($booking['Amount'] ?? 0, 2) ?> ETB</p>
                            <span class="booking-status status-<?= strtolower($booking['PaymentStatus'] ?? 'pending') ?>">
                                <?= ucfirst($booking['PaymentStatus'] ?? 'Pending') ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                    <a href="tickets.php" class="btn">View All Tickets</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
