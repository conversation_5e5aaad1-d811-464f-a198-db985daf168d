<?php
session_start();
require_once 'db_connect.php';

$message = '';
$error = '';
$verified = false;

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];
    
    try {
        // Convert PDO to mysqli for compatibility
        $host = 'localhost';
        $user = 'root';
        $pass = '';
        $db = 'eventbb';
        
        $conn = new mysqli($host, $user, $pass, $db);
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        // Check if token exists and is not expired
        $stmt = $conn->prepare("SELECT UserId, FirstName, Email, EmailVerified FROM Users WHERE EmailVerificationToken = ? AND TokenExpiry > NOW()");
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            
            if ($user['EmailVerified'] == 1) {
                $message = "Your email has already been verified. You can now log in to your account.";
                $verified = true;
            } else {
                // Verify the email
                $updateStmt = $conn->prepare("UPDATE Users SET EmailVerified = 1, EmailVerificationToken = NULL, TokenExpiry = NULL WHERE EmailVerificationToken = ?");
                $updateStmt->bind_param("s", $token);
                
                if ($updateStmt->execute()) {
                    $message = "Email verification successful! Your account is now active.";
                    $verified = true;
                } else {
                    $error = "Failed to verify email. Please try again.";
                }
                $updateStmt->close();
            }
        } else {
            $error = "Invalid or expired verification token. Please request a new verification email.";
        }
        
        $stmt->close();
        $conn->close();
        
    } catch (Exception $e) {
        $error = "An error occurred during verification: " . $e->getMessage();
    }
} else {
    $error = "No verification token provided.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .verification-container {
            max-width: 600px;
            margin: 100px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .verification-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        .success-icon {
            color: #28a745;
        }
        .error-icon {
            color: #dc3545;
        }
        .verification-message {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .action-buttons {
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 10px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <?php if ($verified): ?>
            <div class="verification-icon success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1>Email Verified Successfully!</h1>
            <div class="verification-message">
                <?= htmlspecialchars($message) ?>
            </div>
            <div class="action-buttons">
                <a href="login.php" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Login to Your Account
                </a>
                <a href="Finalll_updated.php" class="btn btn-secondary">
                    <i class="fas fa-home"></i> Go to Homepage
                </a>
            </div>
        <?php else: ?>
            <div class="verification-icon error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1>Email Verification Failed</h1>
            <div class="verification-message">
                <?= htmlspecialchars($error) ?>
            </div>
            <div class="action-buttons">
                <a href="register.php" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> Register Again
                </a>
                <a href="forgot_password.php" class="btn btn-secondary">
                    <i class="fas fa-envelope"></i> Request New Verification
                </a>
                <a href="Finalll_updated.php" class="btn btn-secondary">
                    <i class="fas fa-home"></i> Go to Homepage
                </a>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <p>Need help? Contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>
