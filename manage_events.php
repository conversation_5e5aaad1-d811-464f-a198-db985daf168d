<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if organizer is logged in
if (!isset($_SESSION['organizer_id'])) {
    header("Location: organizer_login.php");
    exit();
}

$organizerId = $_SESSION['organizer_id'];
$organizerName = $_SESSION['organizer_name'];

// Get organizer's events
try {
    $stmt = $pdo->prepare("
        SELECT e.*,
               COUNT(DISTINCT b.BookingId) as booking_count,
               COALESCE(SUM(b.TotalAmount), 0) as event_revenue,
               COUNT(DISTINCT t.TicketId) as ticket_types,
               SUM(CASE WHEN t.TicketType = 'Regular' THEN t.AvailableTickets ELSE 0 END) as regular_seats,
               SUM(CASE WHEN t.TicketType = 'VIP' THEN t.AvailableTickets ELSE 0 END) as vip_seats,
               SUM(CASE WHEN t.TicketType = 'VVIP' THEN t.AvailableTickets ELSE 0 END) as vvip_seats
        FROM Events e
        LEFT JOIN Bookings b ON e.EventId = b.EventId AND b.PaymentStatus = 'Completed'
        LEFT JOIN Tickets t ON e.EventId = t.EventId
        WHERE e.OrganizerId = ?
        GROUP BY e.EventId
        ORDER BY e.CreatedDate DESC
    ");
    $stmt->execute([$organizerId]);
    $events = $stmt->fetchAll();
} catch (Exception $e) {
    $error = "Error loading events: " . $e->getMessage();
    $events = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Events - Organizer Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .header-actions {
            display: flex;
            gap: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #28a745;
            color: white;
        }
        .btn-primary:hover {
            background: #218838;
        }
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn-secondary:hover {
            background: rgba(255,255,255,0.3);
        }
        .btn-sm {
            padding: 5px 12px;
            font-size: 12px;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        .events-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .events-header {
            background: #f8f9fa;
            padding: 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .events-header h2 {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #333;
        }
        .events-content {
            padding: 0;
        }
        .events-table {
            width: 100%;
            border-collapse: collapse;
        }
        .events-table th,
        .events-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .events-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .events-table tr:hover {
            background: #f8f9fa;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .event-image {
            width: 60px;
            height: 40px;
            object-fit: cover;
            border-radius: 5px;
        }
        .event-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .event-details {
            font-size: 12px;
            color: #666;
        }
        .stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
        }
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            color: #ddd;
        }
        .empty-state h3 {
            margin-bottom: 10px;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }
            .events-table {
                font-size: 14px;
            }
            .events-table th,
            .events-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-cog"></i> Manage Events</h1>
            <div class="header-actions">
                <a href="create_event.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Event
                </a>
                <a href="organizer_dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-dashboard"></i> Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="events-container">
            <div class="events-header">
                <h2><i class="fas fa-list"></i> Your Events (<?= count($events) ?>)</h2>
                <div>
                    <a href="create_event.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Event
                    </a>
                </div>
            </div>
            
            <div class="events-content">
                <?php if (count($events) > 0): ?>
                <table class="events-table">
                    <thead>
                        <tr>
                            <th>Event</th>
                            <th>Date & Time</th>
                            <th>Status</th>
                            <th>Statistics</th>
                            <th>Revenue</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($events as $event): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 15px;">
                                    <?php if ($event['EventImage']): ?>
                                        <img src="<?= htmlspecialchars($event['EventImage']) ?>" alt="Event" class="event-image">
                                    <?php else: ?>
                                        <div style="width: 60px; height: 40px; background: #e9ecef; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-image" style="color: #adb5bd;"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="event-title"><?= htmlspecialchars($event['Title']) ?></div>
                                        <div class="event-details">
                                            <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($event['Place']) ?><br>
                                            <i class="fas fa-tag"></i> <?= htmlspecialchars($event['EventCategory']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong><?= date('M d, Y', strtotime($event['EventDate'])) ?></strong><br>
                                    <small><?= date('H:i', strtotime($event['EventDate'])) ?></small>
                                    <?php if ($event['EndDate']): ?>
                                        <br><small>to <?= date('M d, H:i', strtotime($event['EndDate'])) ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge <?= $event['IsApproved'] ? 'status-approved' : 'status-pending' ?>">
                                    <?= $event['IsApproved'] ? 'Approved' : 'Pending' ?>
                                </span>
                                <?php if ($event['IsFeatured']): ?>
                                    <br><small style="color: #ffc107;"><i class="fas fa-star"></i> Featured</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="stats">
                                    <div class="stat-item">
                                        <i class="fas fa-ticket-alt" style="color: #17a2b8;"></i>
                                        <span><?= $event['booking_count'] ?> bookings</span>
                                    </div>
                                </div>
                                <div class="stats" style="margin-top: 5px;">
                                    <div class="stat-item">
                                        <i class="fas fa-users" style="color: #28a745;"></i>
                                        <span><?= $event['MaxCapacity'] ?> capacity</span>
                                    </div>
                                </div>
                                <div class="stats" style="margin-top: 5px; font-size: 11px;">
                                    <div style="display: flex; gap: 8px;">
                                        <?php if ($event['regular_seats'] > 0): ?>
                                            <span style="color: #6c757d;">R: <?= $event['regular_seats'] ?></span>
                                        <?php endif; ?>
                                        <?php if ($event['vip_seats'] > 0): ?>
                                            <span style="color: #ffc107;">V: <?= $event['vip_seats'] ?></span>
                                        <?php endif; ?>
                                        <?php if ($event['vvip_seats'] > 0): ?>
                                            <span style="color: #dc3545;">VV: <?= $event['vvip_seats'] ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong><?= number_format($event['event_revenue'], 2) ?> ETB</strong><br>
                                <small style="color: #666;">Base: <?= number_format($event['TicketPrice'], 2) ?> ETB</small>
                            </td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 5px;">
                                    <a href="edit_event.php?id=<?= $event['EventId'] ?>" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="event_details.php?id=<?= $event['EventId'] ?>" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <?php if ($event['IsApproved']): ?>
                                        <a href="event_analytics.php?id=<?= $event['EventId'] ?>" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-chart-line"></i> Analytics
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-calendar-plus"></i>
                    <h3>No Events Yet</h3>
                    <p>You haven't created any events yet. Start by creating your first event!</p>
                    <a href="create_event.php" class="btn btn-primary" style="margin-top: 20px;">
                        <i class="fas fa-plus"></i> Create Your First Event
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
