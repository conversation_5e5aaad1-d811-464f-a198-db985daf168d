-- =====================================================
-- COMPLETE DATABASE SETUP FOR EVENT BOOKING SYSTEM
-- =====================================================
-- This script creates a complete database with all necessary tables
-- for the event booking system including analytics, registration, and admin features

-- Drop existing database if it exists and create new one
DROP DATABASE IF EXISTS eventBB;
CREATE DATABASE eventBB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE eventBB;

CREATE TABLE EventOrganizers (
    OrganizerId CHAR(9) PRIMARY KEY,
    OrganizerName VARCHAR(100) NOT NULL,
    Email VARCHAR(100) UNIQUE NOT NULL,
    PasswordHash VARCHAR(255) NOT NULL,
    PhoneNumber VARCHAR(20),
    Address TEXT,
    City VARCHAR(50) DEFAULT 'Addis Ababa',
    IsApproved TINYINT(1) DEFAULT 0,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE Users (
    UserId CHAR(9) PRIMARY KEY,
    FirstName VARCHAR(50) NOT NULL,
    LastName VARCHAR(50) NOT NULL,
    Gender ENUM('Male', 'Female') NOT NULL,
    DateOfBirth DATE NOT NULL,
    PhoneNumber VARCHAR(20) NOT NULL,
    Email VARCHAR(100) UNIQUE NOT NULL,
    PasswordHash VARCHAR(255) NOT NULL,
    HouseNumber INT,
    SubCity VARCHAR(50),
    City VARCHAR(50) DEFAULT 'Addis Ababa',
    -- Email verification columns
    EmailVerified TINYINT(1) DEFAULT 0,
    EmailVerificationToken VARCHAR(64),
    TokenExpiry DATETIME,
    -- Registration tracking
    RegistrationDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    LastLoginDate TIMESTAMP NULL,
    IsActive TINYINT(1) DEFAULT 1,
    ProfilePicture VARCHAR(255),
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE Admins (
    AdminId CHAR(9) PRIMARY KEY,
    FirstName VARCHAR(50) NOT NULL,
    LastName VARCHAR(50) NOT NULL,
    Gender ENUM('Male', 'Female') NOT NULL,
    DateOfBirth DATE NOT NULL,
    PhoneNumber VARCHAR(20) NOT NULL,
    Email VARCHAR(100) UNIQUE NOT NULL,
    PasswordHash VARCHAR(255) NOT NULL,
    HouseNumber INT,
    SubCity VARCHAR(50),
    City VARCHAR(50) DEFAULT 'Addis Ababa',
    Role ENUM('SuperAdmin', 'Admin', 'Moderator') DEFAULT 'Admin',
    UserId CHAR(9),
    OrganizerId CHAR(9),
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    LastLoginDate TIMESTAMP NULL,
    IsActive TINYINT(1) DEFAULT 1,
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    FOREIGN KEY (OrganizerId) REFERENCES EventOrganizers(OrganizerId) ON DELETE SET NULL
);


CREATE TABLE Events (
    EventId CHAR(9) PRIMARY KEY,
    Title VARCHAR(200) NOT NULL,
    Description TEXT,
    EventDate DATETIME NOT NULL,
    EndDate DATETIME,
    Place VARCHAR(200) NOT NULL,
    Address TEXT,
    EventCategory VARCHAR(50) DEFAULT 'General',
    MaxCapacity INT DEFAULT 1000,
    TicketPrice DECIMAL(10,2) DEFAULT 0.00,
    EventImage VARCHAR(255),
    OrganizerId CHAR(9),
    IsApproved TINYINT(1) DEFAULT 0,
    IsFeatured TINYINT(1) DEFAULT 0,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (OrganizerId) REFERENCES EventOrganizers(OrganizerId) ON DELETE SET NULL,
    INDEX idx_event_date (EventDate),
    INDEX idx_category (EventCategory),
    INDEX idx_approved (IsApproved)
);

CREATE TABLE Tickets (
    TicketId CHAR(9) PRIMARY KEY,
    EventId CHAR(9) NOT NULL,
    SeatNumber VARCHAR(10),
    SeatRow VARCHAR(5),
    SeatSection VARCHAR(20),
    Status ENUM('Available', 'Reserved', 'Sold', 'Cancelled') DEFAULT 'Available',
    TicketType ENUM('VIP', 'Regular', 'VVIP', 'Student', 'Group') DEFAULT 'Regular',
    Price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    AvailableTickets INT DEFAULT 1,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (EventId) REFERENCES Events(EventId) ON DELETE CASCADE,
    INDEX idx_event_status (EventId, Status),
    INDEX idx_ticket_type (TicketType)
);


CREATE TABLE Bookings (
    BookingId CHAR(9) PRIMARY KEY,
    UserId CHAR(9) NOT NULL,
    EventId CHAR(9) NOT NULL,
    TicketId CHAR(9),
    Quantity INT DEFAULT 1,
    TotalAmount DECIMAL(10,2) NOT NULL,
    BookingDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PaymentStatus ENUM('Pending', 'Completed', 'Failed', 'Cancelled', 'Refunded') DEFAULT 'Pending',
    BookingStatus ENUM('Active', 'Cancelled', 'Expired') DEFAULT 'Active',
    -- Customer information (for guest bookings)
    CustomerName VARCHAR(100),
    CustomerEmail VARCHAR(100),
    CustomerPhone VARCHAR(20),
    -- Booking details
    SpecialRequests TEXT,
    BookingReference VARCHAR(20) UNIQUE,
    ExpiryDate DATETIME,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    FOREIGN KEY (EventId) REFERENCES Events(EventId) ON DELETE CASCADE,
    FOREIGN KEY (TicketId) REFERENCES Tickets(TicketId) ON DELETE SET NULL,
    INDEX idx_user_bookings (UserId),
    INDEX idx_event_bookings (EventId),
    INDEX idx_booking_date (BookingDate),
    INDEX idx_payment_status (PaymentStatus)
);

CREATE TABLE Payments (
    PaymentId CHAR(9) PRIMARY KEY,
    BookingId CHAR(9) NOT NULL,
    Amount DECIMAL(10,2) NOT NULL,
    PaymentMethod ENUM('Cash', 'Card', 'Bank Transfer', 'Mobile Money', 'Online') DEFAULT 'Cash',
    PaymentStatus ENUM('Pending', 'Completed', 'Failed', 'Cancelled', 'Refunded') DEFAULT 'Pending',
    PaymentDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    TransactionId VARCHAR(100),
    PaymentGateway VARCHAR(50),
    PaymentReference VARCHAR(100),
    ProcessedBy CHAR(9), -- Admin or system user who processed
    Notes TEXT,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (BookingId) REFERENCES Bookings(BookingId) ON DELETE CASCADE,
    INDEX idx_payment_date (PaymentDate),
    INDEX idx_payment_status (PaymentStatus),
    INDEX idx_payment_method (PaymentMethod)
);


CREATE TABLE SystemSettings (
    SettingKey VARCHAR(100) PRIMARY KEY,
    SettingValue TEXT,
    SettingType ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    Description TEXT,
    Category VARCHAR(50) DEFAULT 'General',
    IsEditable TINYINT(1) DEFAULT 1,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UpdatedBy CHAR(9)
);

CREATE TABLE SupportTickets (
    TicketId CHAR(9) PRIMARY KEY,
    UserId CHAR(9),
    Subject VARCHAR(200) NOT NULL,
    Message TEXT NOT NULL,
    Category ENUM('Technical', 'Booking', 'Payment', 'General', 'Complaint') DEFAULT 'General',
    Priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    Status ENUM('Open', 'In Progress', 'Resolved', 'Closed') DEFAULT 'Open',
    AssignedTo CHAR(9),
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ResolvedDate TIMESTAMP NULL,
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE SET NULL,
    FOREIGN KEY (AssignedTo) REFERENCES Admins(AdminId) ON DELETE SET NULL,
    INDEX idx_status (Status),
    INDEX idx_priority (Priority),
    INDEX idx_category (Category)
);


CREATE TABLE RegistrationDebug (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    AttemptTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Email VARCHAR(100),
    ErrorType VARCHAR(50),
    ErrorMessage TEXT,
    UserAgent TEXT,
    IpAddress VARCHAR(45),
    INDEX idx_attempt_time (AttemptTime),
    INDEX idx_email (Email)
);

CREATE TABLE AuditLog (
    LogId INT AUTO_INCREMENT PRIMARY KEY,
    TableName VARCHAR(50) NOT NULL,
    RecordId VARCHAR(20) NOT NULL,
    Action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    OldValues JSON,
    NewValues JSON,
    UserId CHAR(9),
    UserType ENUM('User', 'Admin', 'Organizer', 'System') DEFAULT 'System',
    IpAddress VARCHAR(45),
    UserAgent TEXT,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_table_record (TableName, RecordId),
    INDEX idx_user_action (UserId, Action),
    INDEX idx_created_date (CreatedDate)
);

INSERT INTO SystemSettings (SettingKey, SettingValue, SettingType, Description, Category) VALUES
('site_name', 'Addis Tickets', 'string', 'Website name', 'General'),
('site_email', '<EMAIL>', 'string', 'Main contact email', 'General'),
('site_phone', '+251911000000', 'string', 'Main contact phone', 'General'),
('currency', 'ETB', 'string', 'Default currency', 'General'),
('timezone', 'Africa/Addis_Ababa', 'string', 'Default timezone', 'General'),
('max_tickets_per_booking', '10', 'number', 'Maximum tickets per booking', 'Booking'),
('booking_expiry_minutes', '30', 'number', 'Booking expiry time in minutes', 'Booking'),
('email_verification_required', '1', 'boolean', 'Require email verification for registration', 'Registration'),
('auto_approve_events', '0', 'boolean', 'Automatically approve new events', 'Events'),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode', 'System');

-- =====================================================
-- INSERT SAMPLE DATA FOR TESTING
-- =====================================================

-- Sample Event Organizers
INSERT INTO EventOrganizers (OrganizerId, OrganizerName, Email, PasswordHash, PhoneNumber, IsApproved) VALUES
('ORG000001', 'Addis Events Co.', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+251911111111', 1),
('ORG000002', 'Cultural Center', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+251922222222', 1),
('ORG000003', 'Sports Events Ltd', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+251933333333', 1);

-- Sample Users
INSERT INTO Users (UserId, FirstName, LastName, Gender, DateOfBirth, PhoneNumber, Email, PasswordHash, SubCity, EmailVerified) VALUES
('USR000001', 'Natu', 'teferi', 'Male', '1990-01-15', '+251911234567', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Bole', 1),
('USR000002', 'ribca', 'zewde', 'Female', '1992-05-20', '+251922345678', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kirkos', 1),
('USR000003', 'samuel', 'abebaw', 'Male', '1988-12-10', '+251933456789', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Yeka', 1),
('USR000004', 'dagim', 'menu', 'male', '1995-03-25', '+251944567890', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Addis Ketema', 1),
('USR000005', 'kalu', 'cufa', 'Male', '1987-08-14', '+251955678901', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Gulele', 1);
-- Sample Admin
INSERT INTO Admins (AdminId, FirstName, LastName, Gender, DateOfBirth, PhoneNumber, Email, PasswordHash, Role, UserId) VALUES
('ADM000001', 'Admin', 'User', 'Male', '1985-01-01', '+251900000000', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'SuperAdmin', 'USR000001');

-- Sample Events
INSERT INTO Events (EventId, Title, Description, EventDate, EndDate, Place, Address, EventCategory, MaxCapacity, TicketPrice, OrganizerId, IsApproved, IsFeatured) VALUES
('EVT000001', 'Music Concert 2024', 'Annual music concert featuring local and international artists', '2024-12-25 19:00:00', '2024-12-25 23:00:00', 'Addis Ababa Stadium', 'Stadium Area, Addis Ababa', 'Music', 5000, 500.00, 'ORG000001', 1, 1),
('EVT000002', 'Football Championship', 'Local football championship final match', '2024-12-30 15:00:00', '2024-12-30 17:00:00', 'Addis Ababa Stadium', 'Stadium Area, Addis Ababa', 'Sports', 10000, 200.00, 'ORG000003', 1, 1),
('EVT000003', 'Cultural Festival', 'Traditional Ethiopian cultural festival', '2025-01-15 10:00:00', '2025-01-15 18:00:00', 'Cultural Center', 'Meskel Square, Addis Ababa', 'Culture', 2000, 150.00, 'ORG000002', 1, 0),
('EVT000004', 'Tech Conference 2025', 'Annual technology and innovation conference', '2025-02-10 09:00:00', '2025-02-10 17:00:00', 'Skylight Hotel', 'Bole, Addis Ababa', 'Technology', 500, 800.00, 'ORG000001', 1, 1),
('EVT000005', 'Art Exhibition', 'Contemporary Ethiopian art exhibition', '2025-01-20 10:00:00', '2025-01-27 18:00:00', 'National Museum', 'Arat Kilo, Addis Ababa', 'Art', 300, 100.00, 'ORG000002', 1, 0);

-- Sample Tickets
INSERT INTO Tickets (TicketId, EventId, SeatNumber, SeatRow, SeatSection, Status, TicketType, Price, AvailableTickets) VALUES
-- Music Concert Tickets
('TCK000001', 'EVT000001', '1', 'A', 'VIP', 'Available', 'VIP', 1000.00, 100),
('TCK000002', 'EVT000001', '1', 'B', 'Regular', 'Available', 'Regular', 500.00, 500),
('TCK000003', 'EVT000001', '1', 'C', 'Regular', 'Available', 'Regular', 500.00, 500),
-- Football Championship Tickets
('TCK000004', 'EVT000002', '1', 'A', 'VIP', 'Available', 'VIP', 400.00, 200),
('TCK000005', 'EVT000002', '1', 'B', 'Regular', 'Available', 'Regular', 200.00, 1000),
-- Cultural Festival Tickets
('TCK000006', 'EVT000003', '1', 'A', 'General', 'Available', 'Regular', 150.00, 500),
('TCK000007', 'EVT000003', '1', 'B', 'General', 'Available', 'Student', 75.00, 200),
-- Tech Conference Tickets
('TCK000008', 'EVT000004', '1', 'A', 'Conference', 'Available', 'Regular', 800.00, 300),
('TCK000009', 'EVT000004', '1', 'B', 'Conference', 'Available', 'Student', 400.00, 100),
-- Art Exhibition Tickets
('TCK000010', 'EVT000005', '1', 'A', 'Gallery', 'Available', 'Regular', 100.00, 200);

-- Sample Bookings (for analytics data)
INSERT INTO Bookings (BookingId, UserId, EventId, TicketId, Quantity, TotalAmount, BookingDate, PaymentStatus, BookingStatus, BookingReference) VALUES
('BKG000001', 'USR000001', 'EVT000001', 'TCK000002', 2, 1000.00, '2024-12-01 10:30:00', 'Completed', 'Active', 'REF001'),
('BKG000002', 'USR000002', 'EVT000002', 'TCK000005', 4, 800.00, '2024-12-02 14:15:00', 'Completed', 'Active', 'REF002'),
('BKG000003', 'USR000003', 'EVT000003', 'TCK000006', 1, 150.00, '2024-12-03 09:45:00', 'Completed', 'Active', 'REF003'),
('BKG000004', 'USR000004', 'EVT000001', 'TCK000001', 1, 1000.00, '2024-12-04 16:20:00', 'Completed', 'Active', 'REF004'),
('BKG000005', 'USR000005', 'EVT000004', 'TCK000008', 1, 800.00, '2024-12-05 11:10:00', 'Pending', 'Active', 'REF005'),
('BKG000006', 'USR000001', 'EVT000005', 'TCK000010', 2, 200.00, '2024-12-06 13:30:00', 'Completed', 'Active', 'REF006'),
('BKG000007', 'USR000002', 'EVT000003', 'TCK000007', 3, 225.00, '2024-12-07 15:45:00', 'Completed', 'Active', 'REF007');

-- Sample Payments (corresponding to completed bookings)
INSERT INTO Payments (PaymentId, BookingId, Amount, PaymentMethod, PaymentStatus, PaymentDate, TransactionId) VALUES
('PAY000001', 'BKG000001', 1000.00, 'Card', 'Completed', '2024-12-01 10:35:00', 'TXN001'),
('PAY000002', 'BKG000002', 800.00, 'Mobile Money', 'Completed', '2024-12-02 14:20:00', 'TXN002'),
('PAY000003', 'BKG000003', 150.00, 'Cash', 'Completed', '2024-12-03 09:50:00', 'TXN003'),
('PAY000004', 'BKG000004', 1000.00, 'Bank Transfer', 'Completed', '2024-12-04 16:25:00', 'TXN004'),
('PAY000005', 'BKG000005', 800.00, 'Card', 'Pending', '2024-12-05 11:15:00', 'TXN005'),
('PAY000006', 'BKG000006', 200.00, 'Mobile Money', 'Completed', '2024-12-06 13:35:00', 'TXN006'),
('PAY000007', 'BKG000007', 225.00, 'Cash', 'Completed', '2024-12-07 15:50:00', 'TXN007');

-- =====================================================
-- CREATE USEFUL VIEWS FOR ANALYTICS
-- =====================================================

-- View for Event Analytics
CREATE VIEW EventAnalytics AS
SELECT
    e.EventId,
    e.Title,
    e.EventDate,
    e.Place,
    e.EventCategory,
    e.TicketPrice,
    eo.OrganizerName,
    COUNT(DISTINCT b.BookingId) as total_bookings,
    COUNT(DISTINCT b.UserId) as unique_customers,
    COALESCE(SUM(b.TotalAmount), 0) as event_revenue,
    COALESCE(SUM(b.Quantity), 0) as tickets_sold
FROM Events e
LEFT JOIN EventOrganizers eo ON e.OrganizerId = eo.OrganizerId
LEFT JOIN Bookings b ON e.EventId = b.EventId AND b.PaymentStatus = 'Completed'
GROUP BY e.EventId;

-- View for User Analytics
CREATE VIEW UserAnalytics AS
SELECT
    u.UserId,
    u.FirstName,
    u.LastName,
    u.Email,
    u.RegistrationDate,
    u.LastLoginDate,
    COUNT(DISTINCT b.BookingId) as total_bookings,
    COALESCE(SUM(b.TotalAmount), 0) as total_spent,
    COUNT(DISTINCT b.EventId) as events_attended
FROM Users u
LEFT JOIN Bookings b ON u.UserId = b.UserId AND b.PaymentStatus = 'Completed'
GROUP BY u.UserId;

-- View for Revenue Analytics
CREATE VIEW RevenueAnalytics AS
SELECT
    DATE(p.PaymentDate) as payment_date,
    COUNT(DISTINCT p.PaymentId) as total_transactions,
    COUNT(DISTINCT b.UserId) as unique_customers,
    SUM(p.Amount) as total_revenue,
    AVG(p.Amount) as average_transaction,
    p.PaymentMethod
FROM Payments p
JOIN Bookings b ON p.BookingId = b.BookingId
WHERE p.PaymentStatus = 'Completed'
GROUP BY DATE(p.PaymentDate), p.PaymentMethod;

-- =====================================================
-- CREATE INDEXES FOR BETTER PERFORMANCE
-- =====================================================

-- Additional indexes for analytics queries
CREATE INDEX idx_users_registration_date ON Users(RegistrationDate);
CREATE INDEX idx_events_category_date ON Events(EventCategory, EventDate);
CREATE INDEX idx_bookings_date_status ON Bookings(BookingDate, PaymentStatus);
CREATE INDEX idx_payments_date_status ON Payments(PaymentDate, PaymentStatus);

-- =====================================================
-- FINAL SUCCESS MESSAGE
-- =====================================================
SELECT 'Database setup completed successfully!' as Status,
       'All tables created with sample data' as Message,
       'Ready for testing analytics and all features' as Note;
