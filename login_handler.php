<?php
session_start();
require_once 'db_connect.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: login.php");
    exit();
}

$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
$password = $_POST['password'];

// Get and sanitize input
$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
$password = $_POST['password'];

// Validate inputs
if (empty($email) || empty($password)) {
    header("Location: login.php?error=emptyfields");
    exit();
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    header("Location: login.php?error=invalidemail");
    exit();
}

try {
    // Get FIRST NAME along with other user data
    $stmt = $pdo->prepare("SELECT UserId, FirstName, Email, PasswordHash FROM Users WHERE Email = ? LIMIT 1");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        if (password_verify($password, $user['PasswordHash'])) {
            session_regenerate_id(true);
            
            // Set ALL session variables including first_name
            $_SESSION['user_id'] = $user['UserId'];
            $_SESSION['user_email'] = $user['Email'];
            $_SESSION['first_name'] = $user['FirstName']; // THIS WAS MISSING
            $_SESSION['loggedin'] = true;

            header("Location: Finalll_updated.php");
            exit();
        } else {
            sleep(1);
            header("Location: login.php?error=wrongpassword");
            exit();
        }
    } else {
        sleep(1);
        header("Location: login.php?error=usernotfound");
        exit();
    }
} catch (PDOException $e) {
    error_log("Login error: ".$e->getMessage());
    header("Location: login.php?error=dberror");
    exit();
}
?>