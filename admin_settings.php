<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if user is admin - SECURE VERSION
if (!isset($_SESSION['admin_loggedin']) || $_SESSION['admin_loggedin'] !== true) {
    // Redirect to admin login if not authenticated
    header("Location: admin_login.php");
    exit();
}

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$message = '';
$error = '';

// Handle settings updates
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        $action = $_POST['action'] ?? '';
        
        try {
            switch ($action) {
                case 'update_payment_methods':
                    $telebirrEnabled = isset($_POST['telebirr_enabled']) ? 1 : 0;
                    $cbeEnabled = isset($_POST['cbe_enabled']) ? 1 : 0;
                    $cashEnabled = isset($_POST['cash_enabled']) ? 1 : 0;
                    
                    // Update or insert payment method settings
                    $stmt = $pdo->prepare("
                        INSERT INTO SystemSettings (SettingKey, SettingValue) 
                        VALUES ('payment_telebirr_enabled', ?), ('payment_cbe_enabled', ?), ('payment_cash_enabled', ?)
                        ON DUPLICATE KEY UPDATE SettingValue = VALUES(SettingValue)
                    ");
                    $stmt->execute([$telebirrEnabled, $cbeEnabled, $cashEnabled]);
                    
                    $message = "Payment method settings updated successfully.";
                    break;
                    
                case 'update_refund_policy':
                    $refundDays = (int)($_POST['refund_days'] ?? 2);
                    $partialRefundHours = (int)($_POST['partial_refund_hours'] ?? 24);
                    $partialRefundPercentage = (int)($_POST['partial_refund_percentage'] ?? 50);
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO SystemSettings (SettingKey, SettingValue) 
                        VALUES ('refund_full_days', ?), ('refund_partial_hours', ?), ('refund_partial_percentage', ?)
                        ON DUPLICATE KEY UPDATE SettingValue = VALUES(SettingValue)
                    ");
                    $stmt->execute([$refundDays, $partialRefundHours, $partialRefundPercentage]);
                    
                    $message = "Refund policy updated successfully.";
                    break;
                    
                case 'update_email_settings':
                    $emailEnabled = isset($_POST['email_enabled']) ? 1 : 0;
                    $smtpHost = trim($_POST['smtp_host'] ?? '');
                    $smtpPort = (int)($_POST['smtp_port'] ?? 587);
                    $smtpUsername = trim($_POST['smtp_username'] ?? '');
                    $smtpPassword = trim($_POST['smtp_password'] ?? '');
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO SystemSettings (SettingKey, SettingValue) 
                        VALUES ('email_enabled', ?), ('smtp_host', ?), ('smtp_port', ?), ('smtp_username', ?), ('smtp_password', ?)
                        ON DUPLICATE KEY UPDATE SettingValue = VALUES(SettingValue)
                    ");
                    $stmt->execute([$emailEnabled, $smtpHost, $smtpPort, $smtpUsername, $smtpPassword]);
                    
                    $message = "Email settings updated successfully.";
                    break;
                    
                case 'add_faq':
                    $question = trim($_POST['faq_question'] ?? '');
                    $answer = trim($_POST['faq_answer'] ?? '');
                    $sortOrder = (int)($_POST['sort_order'] ?? 0);
                    
                    if (!empty($question) && !empty($answer)) {
                        $faqId = 'FAQ' . substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 6);
                        $stmt = $pdo->prepare("
                            INSERT INTO FAQs (FAQId, Question, Answer, SortOrder, IsActive, CreatedDate) 
                            VALUES (?, ?, ?, ?, 1, NOW())
                        ");
                        $stmt->execute([$faqId, $question, $answer, $sortOrder]);
                        
                        $message = "FAQ added successfully.";
                    } else {
                        $error = "Please fill in both question and answer.";
                    }
                    break;
                    
                case 'update_system_info':
                    $siteName = trim($_POST['site_name'] ?? '');
                    $siteDescription = trim($_POST['site_description'] ?? '');
                    $contactEmail = trim($_POST['contact_email'] ?? '');
                    $contactPhone = trim($_POST['contact_phone'] ?? '');
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO SystemSettings (SettingKey, SettingValue) 
                        VALUES ('site_name', ?), ('site_description', ?), ('contact_email', ?), ('contact_phone', ?)
                        ON DUPLICATE KEY UPDATE SettingValue = VALUES(SettingValue)
                    ");
                    $stmt->execute([$siteName, $siteDescription, $contactEmail, $contactPhone]);
                    
                    $message = "System information updated successfully.";
                    break;
            }
            
        } catch (Exception $e) {
            $error = "Error updating settings: " . $e->getMessage();
        }
    }
}

// Get current settings
try {
    $settingsStmt = $pdo->prepare("SELECT SettingKey, SettingValue FROM SystemSettings");
    $settingsStmt->execute();
    $settingsData = $settingsStmt->fetchAll();
    
    $settings = [];
    foreach ($settingsData as $setting) {
        $settings[$setting['SettingKey']] = $setting['SettingValue'];
    }
    
    // Get FAQs
    $faqStmt = $pdo->prepare("SELECT * FROM FAQs ORDER BY SortOrder, CreatedDate DESC");
    $faqStmt->execute();
    $faqs = $faqStmt->fetchAll();
    
} catch (Exception $e) {
    $settings = [];
    $faqs = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - Addis Tickets Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .nav-links {
            margin-top: 10px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .settings-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .settings-card h3 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .faq-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .faq-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        .faq-item h5 {
            color: #333;
            margin-bottom: 8px;
        }
        .faq-item p {
            color: #666;
            margin: 0;
            font-size: 14px;
        }
        .faq-actions {
            margin-top: 10px;
        }
        .faq-actions button {
            padding: 5px 10px;
            font-size: 12px;
            margin-right: 5px;
        }
        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-cog"></i> System Settings</h1>
        <div class="nav-links">
            <a href="admin.php"><i class="fas fa-arrow-left"></i> Back to Admin</a>
            <a href="admin_analytics.php"><i class="fas fa-chart-line"></i> Analytics</a>
        </div>
    </div>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="settings-grid">
            <!-- Payment Methods -->
            <div class="settings-card">
                <h3><i class="fas fa-credit-card"></i> Payment Methods</h3>
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                    <input type="hidden" name="action" value="update_payment_methods">
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="telebirr" name="telebirr_enabled" 
                               <?= ($settings['payment_telebirr_enabled'] ?? 1) ? 'checked' : '' ?>>
                        <label for="telebirr">Enable Telebirr</label>
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="cbe" name="cbe_enabled" 
                               <?= ($settings['payment_cbe_enabled'] ?? 1) ? 'checked' : '' ?>>
                        <label for="cbe">Enable CBE Birr</label>
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="cash" name="cash_enabled" 
                               <?= ($settings['payment_cash_enabled'] ?? 1) ? 'checked' : '' ?>>
                        <label for="cash">Enable Cash Payment</label>
                    </div>
                    
                    <button type="submit" class="btn">Update Payment Methods</button>
                </form>
            </div>

            <!-- Refund Policy -->
            <div class="settings-card">
                <h3><i class="fas fa-undo"></i> Refund Policy</h3>
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                    <input type="hidden" name="action" value="update_refund_policy">
                    
                    <div class="form-group">
                        <label for="refund_days">Full Refund (Days before event)</label>
                        <input type="number" id="refund_days" name="refund_days" 
                               value="<?= $settings['refund_full_days'] ?? 2 ?>" min="1" max="30">
                    </div>
                    
                    <div class="form-group">
                        <label for="partial_refund_hours">Partial Refund (Hours before event)</label>
                        <input type="number" id="partial_refund_hours" name="partial_refund_hours" 
                               value="<?= $settings['refund_partial_hours'] ?? 24 ?>" min="1" max="72">
                    </div>
                    
                    <div class="form-group">
                        <label for="partial_refund_percentage">Partial Refund Percentage</label>
                        <input type="number" id="partial_refund_percentage" name="partial_refund_percentage" 
                               value="<?= $settings['refund_partial_percentage'] ?? 50 ?>" min="0" max="100">
                    </div>
                    
                    <button type="submit" class="btn">Update Refund Policy</button>
                </form>
            </div>

            <!-- System Information -->
            <div class="settings-card">
                <h3><i class="fas fa-info-circle"></i> System Information</h3>
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                    <input type="hidden" name="action" value="update_system_info">
                    
                    <div class="form-group">
                        <label for="site_name">Site Name</label>
                        <input type="text" id="site_name" name="site_name" 
                               value="<?= htmlspecialchars($settings['site_name'] ?? 'Addis Tickets') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="site_description">Site Description</label>
                        <textarea id="site_description" name="site_description"><?= htmlspecialchars($settings['site_description'] ?? 'Official Event Booking Platform') ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="contact_email">Contact Email</label>
                        <input type="email" id="contact_email" name="contact_email" 
                               value="<?= htmlspecialchars($settings['contact_email'] ?? '<EMAIL>') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="contact_phone">Contact Phone</label>
                        <input type="text" id="contact_phone" name="contact_phone" 
                               value="<?= htmlspecialchars($settings['contact_phone'] ?? '+251-11-XXX-XXXX') ?>">
                    </div>
                    
                    <button type="submit" class="btn">Update System Info</button>
                </form>
            </div>

            <!-- Email Settings -->
            <div class="settings-card">
                <h3><i class="fas fa-envelope"></i> Email Settings</h3>
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                    <input type="hidden" name="action" value="update_email_settings">
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="email_enabled" name="email_enabled" 
                               <?= ($settings['email_enabled'] ?? 1) ? 'checked' : '' ?>>
                        <label for="email_enabled">Enable Email Notifications</label>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_host">SMTP Host</label>
                        <input type="text" id="smtp_host" name="smtp_host" 
                               value="<?= htmlspecialchars($settings['smtp_host'] ?? 'localhost') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_port">SMTP Port</label>
                        <input type="number" id="smtp_port" name="smtp_port" 
                               value="<?= $settings['smtp_port'] ?? 587 ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_username">SMTP Username</label>
                        <input type="text" id="smtp_username" name="smtp_username" 
                               value="<?= htmlspecialchars($settings['smtp_username'] ?? '') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtp_password">SMTP Password</label>
                        <input type="password" id="smtp_password" name="smtp_password" 
                               value="<?= htmlspecialchars($settings['smtp_password'] ?? '') ?>">
                    </div>
                    
                    <button type="submit" class="btn">Update Email Settings</button>
                </form>
            </div>

            <!-- FAQ Management -->
            <div class="settings-card">
                <h3><i class="fas fa-question-circle"></i> FAQ Management</h3>
                
                <!-- Add New FAQ -->
                <form method="POST" style="margin-bottom: 20px;">
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                    <input type="hidden" name="action" value="add_faq">
                    
                    <div class="form-group">
                        <label for="faq_question">Question</label>
                        <input type="text" id="faq_question" name="faq_question" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="faq_answer">Answer</label>
                        <textarea id="faq_answer" name="faq_answer" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="sort_order">Sort Order</label>
                        <input type="number" id="sort_order" name="sort_order" value="0">
                    </div>
                    
                    <button type="submit" class="btn">Add FAQ</button>
                </form>

                <!-- Existing FAQs -->
                <div class="faq-list">
                    <?php foreach ($faqs as $faq): ?>
                        <div class="faq-item">
                            <h5><?= htmlspecialchars($faq['Question']) ?></h5>
                            <p><?= nl2br(htmlspecialchars($faq['Answer'])) ?></p>
                            <div class="faq-actions">
                                <button class="btn btn-danger" onclick="deleteFAQ('<?= $faq['FAQId'] ?>')">
                                    Delete
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function deleteFAQ(faqId) {
            if (confirm('Are you sure you want to delete this FAQ?')) {
                // Create form to delete FAQ
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                    <input type="hidden" name="action" value="delete_faq">
                    <input type="hidden" name="faq_id" value="${faqId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
