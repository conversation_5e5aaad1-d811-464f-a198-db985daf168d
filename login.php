<?php
// Start session and prevent caching at the very top
session_start();
header("Cache-Control: no-store, no-cache, must-revalidate");
header("Expires: 0");

// Redirect if already logged in
if (isset($_SESSION['user_id']) && $_SESSION['loggedin'] === true) {
    header("Location: Finalll_updated.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Addis Ababa Stadium Tickets</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        /* Login Page Specific Styles */
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a6fd8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .alert-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
        }

        .success-alert, .error-alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
        }

        .success-alert {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .error-alert {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }

        .login-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
            backdrop-filter: blur(10px);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h2 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-header p {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control.error-border {
            border-color: var(--error-color);
            background: #fff5f5;
        }

        .error-message {
            color: var(--error-color);
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .remember-me {
            display: flex;
            align-items: center;
        }

        .remember-me input[type="checkbox"] {
            margin-right: 8px;
        }

        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .submit-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .social-login {
            text-align: center;
            margin: 25px 0;
        }

        .social-login p {
            color: #666;
            margin-bottom: 15px;
            position: relative;
        }

        .social-login p::before,
        .social-login p::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 40%;
            height: 1px;
            background: #e1e5e9;
        }

        .social-login p::before { left: 0; }
        .social-login p::after { right: 0; }

        .social-icons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .social-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .social-icon.facebook {
            background: #3b5998;
        }

        .social-icon.google {
            background: #dd4b39;
        }

        .social-icon.telegram {
            background: #0088cc;
        }

        .social-icon:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }

        .register-link {
            text-align: center;
            margin-top: 20px;
        }

        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        footer {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            text-align: center;
            padding: 20px;
            margin-top: auto;
        }

        footer a {
            color: var(--text-secondary);
            text-decoration: none;
        }

        footer a:hover {
            color: var(--primary-color);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .login-form {
                padding: 30px 20px;
                margin: 20px;
            }

            .login-header h2 {
                font-size: 24px;
            }

            .remember-forgot {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="alert-messages">
        <?php
        // Display registration success message only once
        if (!empty($_SESSION['registration_success']))
        {
            echo '<div class="success-alert"> Registration successful! Please log in with your credentials.</div>';
            unset($_SESSION['registration_success']);
        }
        if (!empty($_GET['error'])) {
            $errorMessages = [
                'wrongpassword' => 'Incorrect password. Please try again.',
                'usernotfound' => 'No account found with this email address. Please register.',
                'emptyfields' => 'Please enter both email and password.',
                'invalidemail' => 'Please enter a valid email address.',
                'loginrequired' => 'Please login to access this page.',
                'dberror' => 'Database error. Please try again later.'
            ];

            if (isset($errorMessages[$_GET['error']])) {
                echo '<div class="error-alert">' . htmlspecialchars($errorMessages[$_GET['error']]) . '</div>';
            } else {
                echo '<div class="error-alert">An error occurred. Please try again.</div>';
            }
        }
        ?>
    </div>

    <header>
        <div class="top-bar">
            <p>Login to book tickets for Addis Ababa Stadium events</p>
        </div>
        <nav class="navbar">
            <div class="logo">
                <img src="logo.png" alt="Addis Ababa Stadium Logo">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
        </nav>
    </header>

    <div class="login-container">
        <div class="login-header">
            <h2>Welcome Back</h2>
            <p>Sign in to access your account and book tickets</p>
        </div>

        <form id="loginForm" class="login-form" action="login_handler.php" method="POST">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>" required>
                <div class="error-message" id="emailError">Please enter a valid email address</div>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
                <div class="error-message" id="passwordError">Please enter your password</div>
            </div>

            <div class="remember-forgot">
                <div class="remember-me">
                    <input type="checkbox" id="rememberMe" name="rememberMe">
                    <label for="rememberMe">Remember me</label>
                </div>
                <div class="forgot-password">
                    <a href="forgot_password.php">Forgot password?</a>
                </div>
            </div>

            <button type="submit" class="submit-btn">Sign In</button>

            <div class="social-login">
                <p>Or sign in with</p>
                <div class="social-icons">
                    <div class="social-icon facebook">
                        <i class="fab fa-facebook-f"></i>
                    </div>
                    <div class="social-icon google">
                        <i class="fab fa-google"></i>
                    </div>
                    <div class="social-icon telegram">
                        <i class="fab fa-telegram-plane"></i>
                    </div>
                </div>
            </div>

            <div class="register-link">
                Don't have an account? <a href="register.php">Register here</a>
            <h3>
                Organizer?  <a href="organizer_login.php">login here</a></h3>
            </div>
        </form>
    </div>

    <footer>
        <div class="footer-bottom">
            <p>&copy; <?php echo date('Y'); ?> Addis Tickets. All Rights Reserved. |
               <a href="privacy.php" style="color: #aaa;">Privacy Policy</a> |
               <a href="terms.php" style="color: #aaa;">Terms of Service</a></p>
        </div>
    </footer>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            // Reset previous errors
            document.querySelectorAll('.error-message').forEach(el => el.style.display = 'none');
            document.querySelectorAll('.form-control').forEach(el => el.classList.remove('error-border'));

            // Validate form
            let isValid = true;
            const email = document.getElementById('email');
            const password = document.getElementById('password');

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email.value.match(emailRegex)) {
                document.getElementById('emailError').style.display = 'block';
                email.classList.add('error-border');
                isValid = false;
            }

            // Password validation
            if (password.value.trim() === '') {
                document.getElementById('passwordError').style.display = 'block';
                password.classList.add('error-border');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                document.querySelector('.error-border')?.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });

        // Auto-remove alerts after 5 seconds
        setTimeout(() => {
            document.querySelectorAll('.error-alert, .success-alert').forEach(alert => {
                alert.style.animation = 'fadeOut 0.5s ease-out';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);

        // Social login handlers
        document.querySelectorAll('.social-icon').forEach(icon => {
            icon.addEventListener('click', function() {
                const platform = this.classList.contains('facebook') ? 'facebook' :
                                this.classList.contains('google') ? 'google' : 'telegram';
                window.location.href = `${platform}_login.php`;
            });
        });
    </script>
</body>
</html>