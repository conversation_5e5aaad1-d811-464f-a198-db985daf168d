<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';
require_once 'email_system.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
        
        if (!$email) {
            $error = "Please enter a valid email address.";
        } else {
            try {
                // Check if user exists
                $stmt = $pdo->prepare("SELECT UserId, FirstName, Email FROM Users WHERE Email = ?");
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if ($user) {
                    // Generate reset token
                    $resetToken = bin2hex(random_bytes(32));
                    $tokenExpiry = date('Y-m-d H:i:s', strtotime('+1 hour'));
                    
                    // Store reset token
                    $updateStmt = $pdo->prepare("
                        UPDATE Users 
                        SET PasswordResetToken = ?, PasswordResetExpiry = ? 
                        WHERE UserId = ?
                    ");
                    $updateStmt->execute([$resetToken, $tokenExpiry, $user['UserId']]);
                    
                    // Send reset email
                    $resetLink = "http://" . $_SERVER['HTTP_HOST'] . "/reset_password.php?token=" . $resetToken;
                    $emailSent = sendPasswordResetEmail($email, $user['FirstName'], $resetLink);
                    
                    if ($emailSent) {
                        $message = "Password reset instructions have been sent to your email address.";
                        
                        // Log the reset request
                        logSecurityEvent('password_reset_requested', [
                            'user_id' => $user['UserId'],
                            'email' => $email
                        ]);
                    } else {
                        $error = "Failed to send reset email. Please try again later.";
                    }
                } else {
                    // Don't reveal if email exists or not for security
                    $message = "If an account with that email exists, password reset instructions have been sent.";
                }
                
            } catch (Exception $e) {
                $error = "An error occurred. Please try again later.";
                logSecurityEvent('password_reset_error', ['error' => $e->getMessage()]);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .forgot-container {
            max-width: 500px;
            margin: 100px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .forgot-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .forgot-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img alt="Addis Ababa Stadium Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
        </nav>
    </header>

    <div class="forgot-container">
        <div class="forgot-header">
            <i class="fas fa-key forgot-icon"></i>
            <h2>Forgot Password?</h2>
            <p>Enter your email address and we'll send you instructions to reset your password.</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if (!$message): ?>
        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
            
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    placeholder="Enter your registered email address"
                    required
                    autocomplete="email"
                >
            </div>

            <button type="submit" class="btn">
                <i class="fas fa-paper-plane"></i> Send Reset Instructions
            </button>
        </form>

        <div class="info-box">
            <h4><i class="fas fa-info-circle"></i> What happens next?</h4>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>We'll send a secure reset link to your email</li>
                <li>Click the link to create a new password</li>
                <li>The link expires in 1 hour for security</li>
            </ul>
        </div>
        <?php endif; ?>

        <div class="back-link">
            <a href="login.php">
                <i class="fas fa-arrow-left"></i> Back to Login
            </a>
            |
            <a href="register.php">
                <i class="fas fa-user-plus"></i> Create New Account
            </a>
        </div>
    </div>

    <script>
        // Auto-focus email input
        document.getElementById('email')?.focus();
        
        // Form validation
        document.querySelector('form')?.addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            if (!email || !email.includes('@')) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return false;
            }
        });
    </script>
</body>
</html>
