<?php
/**
 * CLEAN DATABASE CONNECTION
 * For fresh event ticketing system setup
 * No pre-configured data
 */

// Database configuration
$host = 'localhost';
$dbname = 'eventbb';
$username = 'root';
$password = '';

try {
    // Create PDO connection with proper error handling
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // Set timezone
    $pdo->exec("SET time_zone = '+03:00'"); // Ethiopia timezone
    
} catch (PDOException $e) {
    // Log error and show user-friendly message
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please check your configuration.");
}

// Legacy mysqli connection for backward compatibility
try {
    $conn = new mysqli($host, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Set charset
    $conn->set_charset("utf8mb4");
    
} catch (Exception $e) {
    error_log("MySQLi connection failed: " . $e->getMessage());
    die("Database connection failed. Please check your configuration.");
}

/**
 * Database utility functions
 */

/**
 * Generate unique ID with prefix
 */
function generateUniqueId($prefix = '', $length = 8) {
    $timestamp = date('ymdHis');
    $random = substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, $length);
    return $prefix . $timestamp . $random;
}

/**
 * Check if database tables exist
 */
function checkDatabaseSetup($pdo) {
    try {
        $tables = [
            'Users', 'EventOrganizers', 'AdminUsers', 'Events', 
            'Tickets', 'Bookings', 'Payments', 'SupportTickets', 
            'FAQ', 'SystemSettings', 'AuditLog', 'UserSessions'
        ];
        
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() == 0) {
                return false;
            }
        }
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Initialize default system settings
 */
function initializeSystemSettings($pdo) {
    try {
        // Check if settings already exist
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM SystemSettings");
        $result = $stmt->fetch();
        
        if ($result['count'] > 0) {
            return true; // Settings already initialized
        }
        
        // Default system settings
        $defaultSettings = [
            ['site_name', 'Addis Tickets', 'Website name', 'General'],
            ['site_description', 'Premium Event Booking Platform', 'Website description', 'General'],
            ['admin_email', '<EMAIL>', 'Administrator email', 'General'],
            ['support_email', '<EMAIL>', 'Support email', 'General'],
            ['support_phone', '+251911000000', 'Support phone number', 'General'],
            ['currency', 'ETB', 'Default currency', 'Payment'],
            ['timezone', 'Africa/Addis_Ababa', 'System timezone', 'General'],
            ['max_tickets_per_booking', '6', 'Maximum tickets per booking', 'Booking'],
            ['booking_timeout_minutes', '15', 'Booking timeout in minutes', 'Booking'],
            ['email_notifications', '1', 'Enable email notifications', 'Notifications'],
            ['sms_notifications', '0', 'Enable SMS notifications', 'Notifications'],
            ['maintenance_mode', '0', 'Maintenance mode status', 'System'],
            ['registration_enabled', '1', 'Allow new user registration', 'User'],
            ['organizer_registration_enabled', '1', 'Allow organizer registration', 'Organizer'],
            ['auto_approve_events', '0', 'Auto approve new events', 'Events'],
            ['featured_events_limit', '6', 'Number of featured events to show', 'Events']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO SystemSettings (SettingKey, SettingValue, Description, Category) 
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to initialize system settings: " . $e->getMessage());
        return false;
    }
}

/**
 * Initialize default FAQ entries
 */
function initializeDefaultFAQ($pdo) {
    try {
        // Check if FAQ already exists
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM FAQ");
        $result = $stmt->fetch();
        
        if ($result['count'] > 0) {
            return true; // FAQ already initialized
        }
        
        // Default FAQ entries
        $defaultFAQ = [
            [
                'How do I book tickets?',
                'To book tickets, browse our events, select your preferred event, choose your seats, and complete the payment process. You will receive a confirmation email with your ticket details.',
                'Booking',
                1
            ],
            [
                'What payment methods do you accept?',
                'We accept Telebirr, CBE Birr, and cash payments at the venue. All online payments are processed securely.',
                'Payment',
                2
            ],
            [
                'Can I cancel or refund my tickets?',
                'Ticket cancellation and refund policies depend on the event organizer. Please check the specific event terms or contact our support team.',
                'Refund',
                3
            ],
            [
                'How do I receive my tickets?',
                'Tickets are delivered digitally to your email and are also available in your account dashboard. You can view and download them anytime.',
                'Tickets',
                4
            ],
            [
                'What if I have technical issues?',
                'If you experience any technical issues, please contact our support team through the contact form or call our support hotline.',
                'Technical',
                5
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO FAQ (Question, Answer, Category, DisplayOrder) 
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($defaultFAQ as $faq) {
            $stmt->execute($faq);
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to initialize FAQ: " . $e->getMessage());
        return false;
    }
}

// Auto-initialize system if tables exist but no data
if (checkDatabaseSetup($pdo)) {
    initializeSystemSettings($pdo);
    initializeDefaultFAQ($pdo);
}

/**
 * Get system setting value
 */
function getSystemSetting($key, $default = null) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT SettingValue FROM SystemSettings WHERE SettingKey = ? AND IsActive = 1");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['SettingValue'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

/**
 * Update system setting
 */
function updateSystemSetting($key, $value) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            UPDATE SystemSettings 
            SET SettingValue = ?, UpdatedDate = NOW() 
            WHERE SettingKey = ?
        ");
        return $stmt->execute([$value, $key]);
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Log user activity
 */
function logActivity($userId, $userType, $action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO AuditLog (UserId, UserType, Action, TableName, RecordId, OldValues, NewValues, IPAddress, UserAgent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        $oldValuesJson = $oldValues ? json_encode($oldValues) : null;
        $newValuesJson = $newValues ? json_encode($newValues) : null;
        
        return $stmt->execute([
            $userId, $userType, $action, $tableName, $recordId, 
            $oldValuesJson, $newValuesJson, $ipAddress, $userAgent
        ]);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
        return false;
    }
}

// Connection success indicator
$db_connected = true;
?>
