<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if organizer is logged in
if (!isset($_SESSION['organizer_id'])) {
    header("Location: organizer_login.php");
    exit();
}

$organizerId = $_SESSION['organizer_id'];
$eventId = $_GET['id'] ?? '';
$message = '';
$error = '';

if (empty($eventId)) {
    header("Location: manage_events.php");
    exit();
}

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM Events WHERE EventId = ? AND OrganizerId = ?");
    $stmt->execute([$eventId, $organizerId]);
    $event = $stmt->fetch();
    
    if (!$event) {
        header("Location: manage_events.php");
        exit();
    }
} catch (Exception $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        try {
            $title = sanitizeInput($_POST['title']);
            $description = sanitizeInput($_POST['description']);
            $eventDate = $_POST['event_date'];
            $endDate = $_POST['end_date'] ?: null;
            $place = sanitizeInput($_POST['place']);
            $address = sanitizeInput($_POST['address']);
            $category = sanitizeInput($_POST['category']);
            $ticketPrice = floatval($_POST['ticket_price']);
            $maxCapacity = intval($_POST['max_capacity']);
            
            // Validate required fields
            if (!$title || !$eventDate || !$place || !$category || $ticketPrice <= 0 || $maxCapacity <= 0) {
                throw new Exception("All required fields must be filled with valid values");
            }
            
            // Update event
            $updateStmt = $pdo->prepare("
                UPDATE Events 
                SET Title = ?, Description = ?, EventDate = ?, EndDate = ?, Place = ?, 
                    Address = ?, EventCategory = ?, TicketPrice = ?, MaxCapacity = ?
                WHERE EventId = ? AND OrganizerId = ?
            ");
            $updateStmt->execute([
                $title, $description, $eventDate, $endDate, $place, 
                $address, $category, $ticketPrice, $maxCapacity, $eventId, $organizerId
            ]);
            
            $message = "Event updated successfully!";
            
            // Refresh event data
            $stmt->execute([$eventId, $organizerId]);
            $event = $stmt->fetch();
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Event - Organizer Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header-content { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { display: flex; align-items: center; gap: 10px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; font-weight: 600; transition: all 0.3s ease; }
        .btn-secondary { background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); }
        .btn-secondary:hover { background: rgba(255,255,255,0.3); }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a6fd8; }
        .container { max-width: 800px; margin: 0 auto; padding: 30px 20px; }
        .form-container { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; color: #333; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        .form-group textarea { height: 100px; resize: vertical; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        @media (max-width: 768px) { .form-row { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-edit"></i> Edit Event</h1>
            <div>
                <a href="manage_events.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Events
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="form-container">
            <h2>Edit Event Details</h2>
            
            <?php if ($message): ?>
                <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>

            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                
                <div class="form-group">
                    <label for="title">Event Title *</label>
                    <input type="text" id="title" name="title" value="<?= htmlspecialchars($event['Title'] ?? '') ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" placeholder="Describe your event..."><?= htmlspecialchars($event['Description'] ?? '') ?></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="event_date">Event Date & Time *</label>
                        <input type="datetime-local" id="event_date" name="event_date" value="<?= date('Y-m-d\TH:i', strtotime($event['EventDate'])) ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="end_date">End Date & Time</label>
                        <input type="datetime-local" id="end_date" name="end_date" value="<?= $event['EndDate'] ? date('Y-m-d\TH:i', strtotime($event['EndDate'])) : '' ?>">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="place">Venue *</label>
                        <input type="text" id="place" name="place" value="<?= htmlspecialchars($event['Place'] ?? '') ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="address">Address</label>
                        <input type="text" id="address" name="address" value="<?= htmlspecialchars($event['Address'] ?? '') ?>" placeholder="Full address">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="category">Category *</label>
                        <select id="category" name="category" required>
                            <option value="">Select Category</option>
                            <option value="Concert" <?= $event['EventCategory'] == 'Concert' ? 'selected' : '' ?>>Concert</option>
                            <option value="Sports" <?= $event['EventCategory'] == 'Sports' ? 'selected' : '' ?>>Sports</option>
                            <option value="Theater" <?= $event['EventCategory'] == 'Theater' ? 'selected' : '' ?>>Theater</option>
                            <option value="Conference" <?= $event['EventCategory'] == 'Conference' ? 'selected' : '' ?>>Conference</option>
                            <option value="Festival" <?= $event['EventCategory'] == 'Festival' ? 'selected' : '' ?>>Festival</option>
                            <option value="Comedy" <?= $event['EventCategory'] == 'Comedy' ? 'selected' : '' ?>>Comedy</option>
                            <option value="Other" <?= $event['EventCategory'] == 'Other' ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="ticket_price">Base Ticket Price (ETB) *</label>
                        <input type="number" id="ticket_price" name="ticket_price" value="<?= $event['TicketPrice'] ?? '' ?>" min="0" step="0.01" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="max_capacity">Maximum Capacity *</label>
                    <input type="number" id="max_capacity" name="max_capacity" value="<?= $event['MaxCapacity'] ?? '' ?>" min="1" required>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Event
                </button>
            </form>
        </div>
    </div>
</body>
</html>
