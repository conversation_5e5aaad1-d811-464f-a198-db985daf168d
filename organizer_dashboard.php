<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if organizer is logged in
if (!isset($_SESSION['organizer_id'])) {
    header("Location: organizer_login.php");
    exit();
}

$organizerId = $_SESSION['organizer_id'];
$organizerName = $_SESSION['organizer_name'];

// Get organizer statistics
try {
    // Total events
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM Events WHERE OrganizerId = ?");
    $stmt->execute([$organizerId]);
    $totalEvents = $stmt->fetch()['total'];
    
    // Approved events
    $stmt = $pdo->prepare("SELECT COUNT(*) as approved FROM Events WHERE OrganizerId = ? AND IsApproved = 1");
    $stmt->execute([$organizerId]);
    $approvedEvents = $stmt->fetch()['approved'];
    
    // Pending events
    $stmt = $pdo->prepare("SELECT COUNT(*) as pending FROM Events WHERE OrganizerId = ? AND IsApproved = 0");
    $stmt->execute([$organizerId]);
    $pendingEvents = $stmt->fetch()['pending'];
    
    // Total bookings
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT b.BookingId) as total_bookings, 
               COALESCE(SUM(b.TotalAmount), 0) as total_revenue
        FROM Bookings b 
        JOIN Events e ON b.EventId = e.EventId 
        WHERE e.OrganizerId = ? AND b.PaymentStatus = 'Completed'
    ");
    $stmt->execute([$organizerId]);
    $bookingStats = $stmt->fetch();
    $totalBookings = $bookingStats['total_bookings'];
    $totalRevenue = $bookingStats['total_revenue'];
    
    // Recent events
    $stmt = $pdo->prepare("
        SELECT e.*, 
               COUNT(DISTINCT b.BookingId) as booking_count,
               COALESCE(SUM(b.TotalAmount), 0) as event_revenue
        FROM Events e 
        LEFT JOIN Bookings b ON e.EventId = b.EventId AND b.PaymentStatus = 'Completed'
        WHERE e.OrganizerId = ? 
        GROUP BY e.EventId
        ORDER BY e.CreatedDate DESC 
        LIMIT 5
    ");
    $stmt->execute([$organizerId]);
    $recentEvents = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Error loading dashboard data: " . $e->getMessage();
    $totalEvents = $approvedEvents = $pendingEvents = $totalBookings = $totalRevenue = 0;
    $recentEvents = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organizer Dashboard - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .header-actions {
            display: flex;
            gap: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #28a745;
            color: white;
        }
        .btn-primary:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn-secondary:hover {
            background: rgba(255,255,255,0.3);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #667eea;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-header h2 {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #333;
        }
        .section-content {
            padding: 20px;
        }
        .events-table {
            width: 100%;
            border-collapse: collapse;
        }
        .events-table th,
        .events-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .events-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .welcome-section h2 {
            margin-bottom: 10px;
        }
        .welcome-section p {
            opacity: 0.9;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .action-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .action-card:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        .action-card a {
            color: white;
            text-decoration: none;
            display: block;
        }
        .action-card i {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
            .quick-actions {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-calendar-alt"></i> Organizer Dashboard</h1>
            <div class="header-actions">
                <a href="create_event.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Event
                </a>
                <a href="manage_events.php" class="btn btn-secondary">
                    <i class="fas fa-cog"></i> Manage Events
                </a>
                <a href="logout.php" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="welcome-section">
            <h2>Welcome back, <?= htmlspecialchars($organizerName) ?>!</h2>
            <p>Manage your events and track your performance from this dashboard.</p>
            
            <div class="quick-actions">
                <div class="action-card">
                    <a href="create_event.php">
                        <i class="fas fa-plus-circle"></i>
                        <strong>Create New Event</strong>
                    </a>
                </div>
                <div class="action-card">
                    <a href="manage_events.php">
                        <i class="fas fa-edit"></i>
                        <strong>Manage Events</strong>
                    </a>
                </div>
                <div class="action-card">
                    <a href="event_analytics.php">
                        <i class="fas fa-chart-line"></i>
                        <strong>View Analytics</strong>
                    </a>
                </div>

            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar"></i>
                </div>
                <div class="stat-number"><?= $totalEvents ?></div>
                <div class="stat-label">Total Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?= $approvedEvents ?></div>
                <div class="stat-label">Approved Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?= $pendingEvents ?></div>
                <div class="stat-label">Pending Approval</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="stat-number"><?= $totalBookings ?></div>
                <div class="stat-label">Total Bookings</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number"><?= number_format($totalRevenue, 0) ?> ETB</div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>

        <!-- Recent Events -->
        <div class="section">
            <div class="section-header">
                <h2><i class="fas fa-list"></i> Recent Events</h2>
                <a href="manage_events.php" class="btn btn-primary">View All</a>
            </div>
            <div class="section-content">
                <?php if (count($recentEvents) > 0): ?>
                <table class="events-table">
                    <thead>
                        <tr>
                            <th>Event Title</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Bookings</th>
                            <th>Revenue</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentEvents as $event): ?>
                        <tr>
                            <td>
                                <strong><?= htmlspecialchars($event['Title']) ?></strong><br>
                                <small><?= htmlspecialchars($event['Place']) ?></small>
                            </td>
                            <td><?= date('M d, Y H:i', strtotime($event['EventDate'])) ?></td>
                            <td>
                                <span class="status-badge <?= $event['IsApproved'] ? 'status-approved' : 'status-pending' ?>">
                                    <?= $event['IsApproved'] ? 'Approved' : 'Pending' ?>
                                </span>
                            </td>
                            <td><?= $event['booking_count'] ?></td>
                            <td><?= number_format($event['event_revenue'], 2) ?> ETB</td>
                            <td>
                                <a href="edit_event.php?id=<?= $event['EventId'] ?>" class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-calendar-plus" style="font-size: 3em; margin-bottom: 20px; color: #ddd;"></i>
                    <h3>No Events Yet</h3>
                    <p>Create your first event to get started!</p>
                    <a href="create_event.php" class="btn btn-primary" style="margin-top: 15px;">
                        <i class="fas fa-plus"></i> Create Event
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
