<?php
/**
 * Email System for Addis Tickets
 * Handles password reset and notification emails
 */

function sendPasswordResetEmail($email, $resetToken, $userName = '') {
    // For development/demo purposes, we'll simulate email sending
    // In production, you would integrate with a real email service like:
    // - PHPMailer with SMTP
    // - SendGrid API
    // - AWS SES
    // - Mailgun API
    
    $resetLink = "http://localhost/final%20pro/reset_password.php?token=" . urlencode($resetToken);
    
    // Log the email details for development
    $logMessage = "Password Reset Email\n";
    $logMessage .= "To: " . $email . "\n";
    $logMessage .= "Name: " . $userName . "\n";
    $logMessage .= "Reset Link: " . $resetLink . "\n";
    $logMessage .= "Token: " . $resetToken . "\n";
    $logMessage .= "Time: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "---\n";
    
    // Save to log file for development
    file_put_contents('email_log.txt', $logMessage, FILE_APPEND | LOCK_EX);
    
    // For development, return success
    // In production, implement actual email sending here
    return true;
}

function sendVerificationEmail($email, $userName, $verificationLink) {
    // For development/demo purposes, we'll simulate email sending
    // In production, you would integrate with a real email service

    // Log the email details for development
    $logMessage = "Email Verification Email\n";
    $logMessage .= "To: " . $email . "\n";
    $logMessage .= "Name: " . $userName . "\n";
    $logMessage .= "Verification Link: " . $verificationLink . "\n";
    $logMessage .= "Time: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "---\n";

    // Save to log file for development
    file_put_contents('email_log.txt', $logMessage, FILE_APPEND | LOCK_EX);

    // For development, return success
    // In production, implement actual email sending here
    return true;
}

function sendWelcomeEmail($email, $userName) {
    // Development simulation
    $logMessage = "Welcome Email\n";
    $logMessage .= "To: " . $email . "\n";
    $logMessage .= "Name: " . $userName . "\n";
    $logMessage .= "Time: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "---\n";

    file_put_contents('email_log.txt', $logMessage, FILE_APPEND | LOCK_EX);
    return true;
}

function sendBookingConfirmationEmail($email, $userName, $bookingId, $eventTitle) {
    // Development simulation
    $logMessage = "Booking Confirmation Email\n";
    $logMessage .= "To: " . $email . "\n";
    $logMessage .= "Name: " . $userName . "\n";
    $logMessage .= "Booking ID: " . $bookingId . "\n";
    $logMessage .= "Event: " . $eventTitle . "\n";
    $logMessage .= "Time: " . date('Y-m-d H:i:s') . "\n";
    $logMessage .= "---\n";
    
    file_put_contents('email_log.txt', $logMessage, FILE_APPEND | LOCK_EX);
    return true;
}

/**
 * Production Email Implementation Example
 * Uncomment and configure for production use
 */
/*
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

function sendEmailProduction($to, $subject, $body, $isHTML = true) {
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = 'smtp.gmail.com'; // Set your SMTP server
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>'; // Your email
        $mail->Password   = 'your-app-password'; // Your app password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'Addis Tickets');
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML($isHTML);
        $mail->Subject = $subject;
        $mail->Body    = $body;
        
        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Email sending failed: {$mail->ErrorInfo}");
        return false;
    }
}
*/

// Development notification function
function showEmailNotification($message) {
    return "📧 Email Simulation: " . $message . " (Check email_log.txt for details)";
}
?>
