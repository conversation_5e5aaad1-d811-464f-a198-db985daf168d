<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if user is admin - SECURE VERSION
if (!isset($_SESSION['admin_loggedin']) || $_SESSION['admin_loggedin'] !== true) {
    header("Location: admin_login.php");
    exit();
}

// Get the active tab
$activeTab = $_GET['tab'] ?? 'users';
$allowedTabs = ['users', 'events', 'tickets', 'organizers'];
if (!in_array($activeTab, $allowedTabs)) {
    $activeTab = 'users';
}

// Handle actions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        $action = $_POST['action'] ?? '';
        
        try {
            if ($action === 'approve_event') {
                $eventId = sanitizeInput($_POST['event_id']);
                $stmt = $pdo->prepare("UPDATE Events SET IsApproved = 1 WHERE EventId = ?");
                $stmt->execute([$eventId]);
                $message = "Event approved successfully!";
            } elseif ($action === 'reject_event') {
                $eventId = sanitizeInput($_POST['event_id']);
                $stmt = $pdo->prepare("UPDATE Events SET IsApproved = 0 WHERE EventId = ?");
                $stmt->execute([$eventId]);
                $message = "Event rejected successfully!";
            } elseif ($action === 'delete_event') {
                $eventId = sanitizeInput($_POST['event_id']);
                $stmt = $pdo->prepare("DELETE FROM Events WHERE EventId = ?");
                $stmt->execute([$eventId]);
                $message = "Event deleted successfully!";
            } elseif ($action === 'toggle_user_status') {
                $userId = sanitizeInput($_POST['user_id']);
                $status = sanitizeInput($_POST['status']);
                $stmt = $pdo->prepare("UPDATE Users SET IsActive = ? WHERE UserId = ?");
                $stmt->execute([$status, $userId]);
                $message = "User status updated successfully!";
            } elseif ($action === 'add_organizer') {
                $organizerName = sanitizeInput($_POST['organizer_name']);
                $email = sanitizeInput($_POST['email']);
                $password = $_POST['password'];
                $phone = sanitizeInput($_POST['phone']);
                $address = sanitizeInput($_POST['address']);
                $city = sanitizeInput($_POST['city']) ?: 'Addis Ababa';

                // Generate organizer ID
                $organizerId = 'ORG' . substr(str_shuffle('0123456789'), 0, 6);
                $passwordHash = password_hash($password, PASSWORD_DEFAULT);

                $stmt = $pdo->prepare("INSERT INTO EventOrganizers (OrganizerId, OrganizerName, Email, PasswordHash, PhoneNumber, Address, City, IsApproved) VALUES (?, ?, ?, ?, ?, ?, ?, 1)");
                $stmt->execute([$organizerId, $organizerName, $email, $passwordHash, $phone, $address, $city]);
                $message = "Organizer added successfully!";
            } elseif ($action === 'toggle_organizer_status') {
                $organizerId = sanitizeInput($_POST['organizer_id']);
                $status = sanitizeInput($_POST['status']);
                $stmt = $pdo->prepare("UPDATE EventOrganizers SET IsApproved = ? WHERE OrganizerId = ?");
                $stmt->execute([$status, $organizerId]);
                $message = "Organizer status updated successfully!";
            } elseif ($action === 'delete_organizer') {
                $organizerId = sanitizeInput($_POST['organizer_id']);
                $stmt = $pdo->prepare("DELETE FROM EventOrganizers WHERE OrganizerId = ?");
                $stmt->execute([$organizerId]);
                $message = "Organizer deleted successfully!";
            }
        } catch (Exception $e) {
            $error = "Action failed: " . $e->getMessage();
        }
    }
}

// Fetch data based on active tab
$data = [];
try {
    if ($activeTab === 'users') {
        $stmt = $pdo->prepare("
            SELECT u.*, COUNT(b.BookingId) as total_bookings, 
                   COALESCE(SUM(p.Amount), 0) as total_spent
            FROM Users u
            LEFT JOIN Bookings b ON u.UserId = b.UserId
            LEFT JOIN Payments p ON b.BookingId = p.BookingId
            GROUP BY u.UserId
            ORDER BY u.RegistrationDate DESC
            LIMIT 50
        ");
        $stmt->execute();
        $data = $stmt->fetchAll();
    } elseif ($activeTab === 'events') {
        $stmt = $pdo->prepare("
            SELECT e.*, o.OrganizerName, COUNT(b.BookingId) as total_bookings,
                   COALESCE(SUM(p.Amount), 0) as total_revenue
            FROM Events e
            LEFT JOIN EventOrganizers o ON e.OrganizerId = o.OrganizerId
            LEFT JOIN Bookings b ON e.EventId = b.EventId
            LEFT JOIN Payments p ON b.BookingId = p.BookingId
            GROUP BY e.EventId
            ORDER BY e.CreatedDate DESC
            LIMIT 50
        ");
        $stmt->execute();
        $data = $stmt->fetchAll();
    } elseif ($activeTab === 'tickets') {
        $stmt = $pdo->prepare("
            SELECT b.*, e.Title as EventTitle, u.FirstName, u.LastName, u.Email,
                   p.Amount, p.PaymentStatus, p.PaymentDate
            FROM Bookings b
            LEFT JOIN Events e ON b.EventId = e.EventId
            LEFT JOIN Users u ON b.UserId = u.UserId
            LEFT JOIN Payments p ON b.BookingId = p.BookingId
            ORDER BY b.BookingDate DESC
            LIMIT 100
        ");
        $stmt->execute();
        $data = $stmt->fetchAll();
    } elseif ($activeTab === 'organizers') {
        $stmt = $pdo->prepare("
            SELECT o.*, COUNT(e.EventId) as total_events,
                   COALESCE(SUM(CASE WHEN e.IsApproved = 1 THEN 1 ELSE 0 END), 0) as approved_events
            FROM EventOrganizers o
            LEFT JOIN Events e ON o.OrganizerId = e.OrganizerId
            GROUP BY o.OrganizerId
            ORDER BY o.CreatedDate DESC
            LIMIT 50
        ");
        $stmt->execute();
        $data = $stmt->fetchAll();
    }
} catch (Exception $e) {
    $error = "Failed to load data: " . $e->getMessage();
    $data = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Management - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleAdmin.css" rel="stylesheet"/>
    <style>
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .back-link {
            margin-bottom: 20px;
        }
        .back-link a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="back-link">
            <a href="admin_dashboard.php">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        <h1>
            <i class="fas fa-cogs"></i>
            <?php
            echo $activeTab === 'users' ? 'User Management' :
                ($activeTab === 'events' ? 'Event Management' :
                ($activeTab === 'organizers' ? 'Organizer Management' : 'Booking Management'));
            ?>
        </h1>

        <?php if ($message): ?>
            <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <!-- Tabs Navigation -->
        <div class="tabs-navigation">
            <button class="tab-btn <?= $activeTab === 'users' ? 'active' : '' ?>" 
                    onclick="location.href='admin.php?tab=users'">
                <i class="fas fa-users"></i> Users
            </button>
            <button class="tab-btn <?= $activeTab === 'events' ? 'active' : '' ?>" 
                    onclick="location.href='admin.php?tab=events'">
                <i class="fas fa-calendar"></i> Events
            </button>
            <button class="tab-btn <?= $activeTab === 'tickets' ? 'active' : '' ?>"
                    onclick="location.href='admin.php?tab=tickets'">
                <i class="fas fa-ticket-alt"></i> Bookings
            </button>
            <button class="tab-btn <?= $activeTab === 'organizers' ? 'active' : '' ?>"
                    onclick="location.href='admin.php?tab=organizers'">
                <i class="fas fa-building"></i> Organizers
            </button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content active">
            <?php if ($activeTab === 'users'): ?>
                <!-- Users Management -->
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>User ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Registration Date</th>
                                <th>Bookings</th>
                                <th>Total Spent</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $user): ?>
                            <tr>
                                <td><?= htmlspecialchars($user['UserId']) ?></td>
                                <td><?= htmlspecialchars($user['FirstName'] . ' ' . $user['LastName']) ?></td>
                                <td><?= htmlspecialchars($user['Email']) ?></td>
                                <td><?= htmlspecialchars($user['PhoneNumber']) ?></td>
                                <td><?= date('M d, Y', strtotime($user['RegistrationDate'])) ?></td>
                                <td><?= number_format($user['total_bookings']) ?></td>
                                <td><?= number_format($user['total_spent'], 2) ?> ETB</td>
                                <td>
                                    <span class="status-badge <?= $user['IsActive'] ? 'status-available' : 'status-cancelled' ?>">
                                        <?= $user['IsActive'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                        <input type="hidden" name="action" value="toggle_user_status">
                                        <input type="hidden" name="user_id" value="<?= $user['UserId'] ?>">
                                        <input type="hidden" name="status" value="<?= $user['IsActive'] ? '0' : '1' ?>">
                                        <button type="submit" class="action-btn <?= $user['IsActive'] ? 'delete-btn' : 'approve-btn' ?>">
                                            <i class="fas fa-<?= $user['IsActive'] ? 'ban' : 'check' ?>"></i>
                                            <?= $user['IsActive'] ? 'Deactivate' : 'Activate' ?>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

            <?php elseif ($activeTab === 'events'): ?>
                <!-- Events Management -->
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Event ID</th>
                                <th>Title</th>
                                <th>Organizer</th>
                                <th>Date</th>
                                <th>Place</th>
                                <th>Bookings</th>
                                <th>Revenue</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $event): ?>
                            <tr>
                                <td><?= htmlspecialchars($event['EventId']) ?></td>
                                <td><?= htmlspecialchars($event['Title']) ?></td>
                                <td><?= htmlspecialchars($event['OrganizerName'] ?? 'Unknown') ?></td>
                                <td><?= date('M d, Y', strtotime($event['EventDate'])) ?></td>
                                <td><?= htmlspecialchars($event['Place']) ?></td>
                                <td><?= number_format($event['total_bookings']) ?></td>
                                <td><?= number_format($event['total_revenue'], 2) ?> ETB</td>
                                <td>
                                    <span class="status-badge <?= $event['IsApproved'] ? 'status-available' : 'status-cancelled' ?>">
                                        <?= $event['IsApproved'] ? 'Approved' : 'Pending' ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (!$event['IsApproved']): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                            <input type="hidden" name="action" value="approve_event">
                                            <input type="hidden" name="event_id" value="<?= $event['EventId'] ?>">
                                            <button type="submit" class="action-btn approve-btn">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                        <input type="hidden" name="action" value="delete_event">
                                        <input type="hidden" name="event_id" value="<?= $event['EventId'] ?>">
                                        <button type="submit" class="action-btn delete-btn" 
                                                onclick="return confirm('Are you sure you want to delete this event?')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

            <?php elseif ($activeTab === 'tickets'): ?>
                <!-- Bookings Management -->
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Booking ID</th>
                                <th>Customer</th>
                                <th>Event</th>
                                <th>Booking Date</th>
                                <th>Amount</th>
                                <th>Payment Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $booking): ?>
                            <tr>
                                <td><?= htmlspecialchars($booking['BookingId']) ?></td>
                                <td>
                                    <?= htmlspecialchars($booking['FirstName'] . ' ' . $booking['LastName']) ?><br>
                                    <small><?= htmlspecialchars($booking['Email']) ?></small>
                                </td>
                                <td><?= htmlspecialchars($booking['EventTitle'] ?? 'Unknown Event') ?></td>
                                <td><?= date('M d, Y H:i', strtotime($booking['BookingDate'])) ?></td>
                                <td><?= number_format($booking['Amount'] ?? 0, 2) ?> ETB</td>
                                <td>
                                    <span class="status-badge <?= ($booking['PaymentStatus'] === 'Completed') ? 'status-available' : 'status-cancelled' ?>">
                                        <?= htmlspecialchars($booking['PaymentStatus'] ?? 'Pending') ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="ticket_detail.php?id=<?= $booking['BookingId'] ?>" class="action-btn update-btn">
                                        <i class="fas fa-eye"></i> View Details
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

            <?php elseif ($activeTab === 'organizers'): ?>
                <!-- Organizers Management -->
                <div style="margin-bottom: 20px;">
                    <button onclick="toggleAddOrganizerForm()" class="action-btn approve-btn">
                        <i class="fas fa-plus"></i> Add New Organizer
                    </button>
                </div>

                <!-- Add Organizer Form (Hidden by default) -->
                <div id="addOrganizerForm" style="display: none; background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3>Add New Organizer</h3>
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                        <input type="hidden" name="action" value="add_organizer">

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label>Organizer Name *</label>
                                <input type="text" name="organizer_name" required class="form-control">
                            </div>
                            <div>
                                <label>Email *</label>
                                <input type="email" name="email" required class="form-control">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label>Password *</label>
                                <input type="password" name="password" required class="form-control">
                            </div>
                            <div>
                                <label>Phone Number</label>
                                <input type="text" name="phone" class="form-control" placeholder="+251...">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label>Address</label>
                                <input type="text" name="address" class="form-control" placeholder="Organizer address">
                            </div>
                            <div>
                                <label>City</label>
                                <input type="text" name="city" class="form-control" placeholder="Addis Ababa" value="Addis Ababa">
                            </div>
                        </div>

                        <div>
                            <button type="submit" class="action-btn approve-btn">
                                <i class="fas fa-save"></i> Add Organizer
                            </button>
                            <button type="button" onclick="toggleAddOrganizerForm()" class="action-btn delete-btn">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </form>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Organizer ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>City</th>
                                <th>Registration Date</th>
                                <th>Total Events</th>
                                <th>Approved Events</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $organizer): ?>
                            <tr>
                                <td><?= htmlspecialchars($organizer['OrganizerId']) ?></td>
                                <td><?= htmlspecialchars($organizer['OrganizerName']) ?></td>
                                <td><?= htmlspecialchars($organizer['Email']) ?></td>
                                <td><?= htmlspecialchars($organizer['PhoneNumber'] ?? 'N/A') ?></td>
                                <td><?= htmlspecialchars($organizer['City'] ?? 'N/A') ?></td>
                                <td><?= date('M d, Y', strtotime($organizer['CreatedDate'] ?? $organizer['RegistrationDate'])) ?></td>
                                <td><?= number_format($organizer['total_events']) ?></td>
                                <td><?= number_format($organizer['approved_events']) ?></td>
                                <td>
                                    <span class="status-badge <?= $organizer['IsApproved'] ? 'status-available' : 'status-cancelled' ?>">
                                        <?= $organizer['IsApproved'] ? 'Approved' : 'Pending' ?>
                                    </span>
                                </td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                        <input type="hidden" name="action" value="toggle_organizer_status">
                                        <input type="hidden" name="organizer_id" value="<?= $organizer['OrganizerId'] ?>">
                                        <input type="hidden" name="status" value="<?= $organizer['IsApproved'] ? '0' : '1' ?>">
                                        <button type="submit" class="action-btn <?= $organizer['IsApproved'] ? 'delete-btn' : 'approve-btn' ?>">
                                            <i class="fas fa-<?= $organizer['IsApproved'] ? 'ban' : 'check' ?>"></i>
                                            <?= $organizer['IsApproved'] ? 'Suspend' : 'Approve' ?>
                                        </button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                        <input type="hidden" name="action" value="delete_organizer">
                                        <input type="hidden" name="organizer_id" value="<?= $organizer['OrganizerId'] ?>">
                                        <button type="submit" class="action-btn delete-btn"
                                                onclick="return confirm('Are you sure you want to delete this organizer? This will also delete all their events.')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function toggleAddOrganizerForm() {
            const form = document.getElementById('addOrganizerForm');
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
        }
    </script>
</body>
</html>
