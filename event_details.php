<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if organizer is logged in
if (!isset($_SESSION['organizer_id'])) {
    header("Location: organizer_login.php");
    exit();
}

$organizerId = $_SESSION['organizer_id'];
$eventId = $_GET['id'] ?? '';

if (empty($eventId)) {
    header("Location: manage_events.php");
    exit();
}

// Get event details with statistics
try {
    $stmt = $pdo->prepare("
        SELECT e.*,
               COUNT(DISTINCT b.BookingId) as total_bookings,
               COALESCE(SUM(b.TotalAmount), 0) as total_revenue,
               COUNT(DISTINCT CASE WHEN b.PaymentStatus = 'Completed' THEN b.BookingId END) as completed_bookings,
               COUNT(DISTINCT CASE WHEN b.PaymentStatus = 'Pending' THEN b.BookingId END) as pending_bookings,
               SUM(CASE WHEN t.TicketType = 'Regular' THEN t.AvailableTickets ELSE 0 END) as regular_seats,
               SUM(CASE WHEN t.TicketType = 'VIP' THEN t.AvailableTickets ELSE 0 END) as vip_seats,
               SUM(CASE WHEN t.TicketType = 'VVIP' THEN t.AvailableTickets ELSE 0 END) as vvip_seats
        FROM Events e
        LEFT JOIN Bookings b ON e.EventId = b.EventId
        LEFT JOIN Tickets t ON e.EventId = t.EventId
        WHERE e.EventId = ? AND e.OrganizerId = ?
        GROUP BY e.EventId
    ");
    $stmt->execute([$eventId, $organizerId]);
    $event = $stmt->fetch();
    
    if (!$event) {
        header("Location: manage_events.php");
        exit();
    }
    
    // Get recent bookings for this event
    $bookingStmt = $pdo->prepare("
        SELECT b.BookingId, b.BookingDate, b.TotalAmount, b.PaymentStatus,
               u.FirstName, u.LastName, u.Email, u.PhoneNumber
        FROM Bookings b
        LEFT JOIN Users u ON b.UserId = u.UserId
        WHERE b.EventId = ?
        ORDER BY b.BookingDate DESC
        LIMIT 10
    ");
    $bookingStmt->execute([$eventId]);
    $recentBookings = $bookingStmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Error loading event details: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Details - <?= htmlspecialchars($event['Title'] ?? 'Event') ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header-content { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { display: flex; align-items: center; gap: 10px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; font-weight: 600; transition: all 0.3s ease; }
        .btn-secondary { background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); }
        .btn-secondary:hover { background: rgba(255,255,255,0.3); }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .container { max-width: 1200px; margin: 0 auto; padding: 30px 20px; }
        .event-overview { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 30px; }
        .event-header { display: flex; gap: 30px; margin-bottom: 30px; }
        .event-image { width: 200px; height: 150px; object-fit: cover; border-radius: 10px; }
        .event-info h2 { margin-bottom: 15px; color: #333; }
        .event-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .detail-item { display: flex; align-items: center; gap: 10px; }
        .detail-item i { color: #667eea; width: 20px; }
        .status-badge { padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 25px; text-align: center; }
        .stat-card .icon { font-size: 2.5em; margin-bottom: 15px; }
        .stat-card .value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-card .label { color: #666; font-size: 14px; }
        .bookings-section { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px; }
        .bookings-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .bookings-table th, .bookings-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e9ecef; }
        .bookings-table th { background: #f8f9fa; font-weight: 600; }
        .payment-status { padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        @media (max-width: 768px) { .event-header { flex-direction: column; } .event-image { width: 100%; height: 200px; } }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-eye"></i> Event Details</h1>
            <div style="display: flex; gap: 10px;">
                <a href="edit_event.php?id=<?= $eventId ?>" class="btn btn-warning">
                    <i class="fas fa-edit"></i> Edit Event
                </a>
                <a href="manage_events.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Events
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Event Overview -->
        <div class="event-overview">
            <div class="event-header">
                <?php if ($event['EventImage']): ?>
                    <img src="<?= htmlspecialchars($event['EventImage']) ?>" alt="Event Image" class="event-image">
                <?php else: ?>
                    <div class="event-image" style="background: #e9ecef; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-image" style="font-size: 3em; color: #adb5bd;"></i>
                    </div>
                <?php endif; ?>
                
                <div class="event-info">
                    <h2><?= htmlspecialchars($event['Title']) ?></h2>
                    <p style="color: #666; margin-bottom: 20px;"><?= htmlspecialchars($event['Description'] ?? 'No description available') ?></p>
                    
                    <div style="margin-bottom: 15px;">
                        <span class="status-badge <?= $event['IsApproved'] ? 'status-approved' : 'status-pending' ?>">
                            <?= $event['IsApproved'] ? 'Approved' : 'Pending Approval' ?>
                        </span>
                        <?php if ($event['IsFeatured']): ?>
                            <span class="status-badge" style="background: #fff3cd; color: #856404; margin-left: 10px;">
                                <i class="fas fa-star"></i> Featured
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="event-details">
                <div class="detail-item">
                    <i class="fas fa-calendar"></i>
                    <span><strong>Date:</strong> <?= date('M d, Y H:i', strtotime($event['EventDate'])) ?></span>
                </div>
                <?php if ($event['EndDate']): ?>
                <div class="detail-item">
                    <i class="fas fa-calendar-check"></i>
                    <span><strong>End:</strong> <?= date('M d, Y H:i', strtotime($event['EndDate'])) ?></span>
                </div>
                <?php endif; ?>
                <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span><strong>Venue:</strong> <?= htmlspecialchars($event['Place']) ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-tag"></i>
                    <span><strong>Category:</strong> <?= htmlspecialchars($event['EventCategory']) ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-money-bill"></i>
                    <span><strong>Base Price:</strong> <?= number_format($event['TicketPrice'], 2) ?> ETB</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-users"></i>
                    <span><strong>Capacity:</strong> <?= number_format($event['MaxCapacity']) ?> people</span>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon" style="color: #28a745;">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="value" style="color: #28a745;"><?= number_format($event['total_bookings']) ?></div>
                <div class="label">Total Bookings</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #17a2b8;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="value" style="color: #17a2b8;"><?= number_format($event['completed_bookings']) ?></div>
                <div class="label">Completed</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #ffc107;">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="value" style="color: #ffc107;"><?= number_format($event['pending_bookings']) ?></div>
                <div class="label">Pending</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #dc3545;">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="value" style="color: #dc3545;"><?= number_format($event['total_revenue'], 2) ?></div>
                <div class="label">Revenue (ETB)</div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="bookings-section">
            <h3><i class="fas fa-list"></i> Recent Bookings</h3>
            
            <?php if (count($recentBookings) > 0): ?>
                <table class="bookings-table">
                    <thead>
                        <tr>
                            <th>Booking ID</th>
                            <th>Customer</th>
                            <th>Contact</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentBookings as $booking): ?>
                        <tr>
                            <td><?= htmlspecialchars($booking['BookingId']) ?></td>
                            <td><?= htmlspecialchars($booking['FirstName'] . ' ' . $booking['LastName']) ?></td>
                            <td>
                                <?= htmlspecialchars($booking['Email']) ?><br>
                                <small><?= htmlspecialchars($booking['PhoneNumber']) ?></small>
                            </td>
                            <td><?= date('M d, Y H:i', strtotime($booking['BookingDate'])) ?></td>
                            <td><?= number_format($booking['TotalAmount'], 2) ?> ETB</td>
                            <td>
                                <span class="payment-status status-<?= strtolower($booking['PaymentStatus']) ?>">
                                    <?= ucfirst($booking['PaymentStatus']) ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #666; margin-top: 20px;">No bookings yet for this event.</p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
