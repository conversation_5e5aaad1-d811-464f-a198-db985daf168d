<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';
require_once 'email_system.php';

$email = $_GET['email'] ?? '';
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
        
        if (!$email) {
            $error = "Please enter a valid email address.";
        } else {
            try {
                // Check if user exists and is not already verified
                $stmt = $pdo->prepare("
                    SELECT UserId, FirstName, Email, EmailVerified 
                    FROM Users 
                    WHERE Email = ?
                ");
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if (!$user) {
                    $error = "No account found with this email address.";
                } elseif ($user['EmailVerified']) {
                    $message = "This email address is already verified. You can log in now.";
                } else {
                    // Generate new verification token
                    $verificationToken = bin2hex(random_bytes(32));
                    $tokenExpiry = date('Y-m-d H:i:s', strtotime('+24 hours'));
                    
                    // Update verification token
                    $updateStmt = $pdo->prepare("
                        UPDATE Users 
                        SET EmailVerificationToken = ?, TokenExpiry = ? 
                        WHERE UserId = ?
                    ");
                    $updateStmt->execute([$verificationToken, $tokenExpiry, $user['UserId']]);
                    
                    // Send verification email
                    $verificationLink = "http://" . $_SERVER['HTTP_HOST'] . "/email_verification.php?token=" . $verificationToken;
                    $emailSent = sendVerificationEmail($email, $user['FirstName'], $verificationLink);
                    
                    if ($emailSent) {
                        $message = "Verification email has been resent to your email address.";
                        
                        // Log the resend
                        logSecurityEvent('verification_email_resent', [
                            'user_id' => $user['UserId'],
                            'email' => $email
                        ]);
                    } else {
                        $error = "Failed to send verification email. Please try again later.";
                    }
                }
                
            } catch (Exception $e) {
                $error = "An error occurred. Please try again later.";
                logSecurityEvent('verification_resend_error', ['error' => $e->getMessage()]);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resend Verification - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .resend-container {
            max-width: 500px;
            margin: 100px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .resend-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .resend-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img alt="Addis Ababa Stadium Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
        </nav>
    </header>

    <div class="resend-container">
        <div class="resend-header">
            <i class="fas fa-envelope-circle-check resend-icon"></i>
            <h2>Resend Verification Email</h2>
            <p>Enter your email address to receive a new verification link.</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                <?php if (strpos($message, 'already verified') !== false): ?>
                    <div style="margin-top: 15px;">
                        <a href="login.php" class="btn">Go to Login</a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if (!$message || strpos($message, 'already verified') === false): ?>
        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
            
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    placeholder="Enter your registered email address"
                    value="<?= htmlspecialchars($email) ?>"
                    required
                    autocomplete="email"
                >
            </div>

            <button type="submit" class="btn">
                <i class="fas fa-paper-plane"></i> Resend Verification Email
            </button>
        </form>

        <div class="info-box">
            <h4><i class="fas fa-info-circle"></i> Important Notes:</h4>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Check your spam/junk folder if you don't see the email</li>
                <li>Verification links expire after 24 hours</li>
                <li>You can only resend verification emails for unverified accounts</li>
            </ul>
        </div>
        <?php endif; ?>

        <div class="back-link">
            <a href="login.php">
                <i class="fas fa-arrow-left"></i> Back to Login
            </a>
            |
            <a href="register.php">
                <i class="fas fa-user-plus"></i> Create New Account
            </a>
        </div>
    </div>

    <script>
        // Auto-focus email input if empty
        const emailInput = document.getElementById('email');
        if (emailInput && !emailInput.value) {
            emailInput.focus();
        }
        
        // Form validation
        document.querySelector('form')?.addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            if (!email || !email.includes('@')) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return false;
            }
        });
    </script>
</body>
</html>
