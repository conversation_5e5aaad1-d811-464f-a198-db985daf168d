<?php
/**
 * Working Checkout Page
 * Handles cart data from localStorage and processes bookings
 */

session_start();
require_once 'db_connect.php';

// Auto-fix database columns if missing
try {
    // Add TotalAmount column to Bookings if missing
    $pdo->exec("ALTER TABLE Bookings ADD COLUMN IF NOT EXISTS TotalAmount DECIMAL(10,2) DEFAULT 0.00");

    // Add payment columns if missing
    $pdo->exec("ALTER TABLE Payments ADD COLUMN IF NOT EXISTS Method VARCHAR(50) DEFAULT 'Cash'");
    $pdo->exec("ALTER TABLE Payments ADD COLUMN IF NOT EXISTS Status VARCHAR(20) DEFAULT 'Pending'");
    $pdo->exec("ALTER TABLE Payments ADD COLUMN IF NOT EXISTS TransactionId VARCHAR(50)");
} catch (Exception $e) {
    // Try without IF NOT EXISTS for older MySQL versions
    try {
        $pdo->exec("ALTER TABLE Bookings ADD COLUMN TotalAmount DECIMAL(10,2) DEFAULT 0.00");
    } catch (Exception $e2) {}
    try {
        $pdo->exec("ALTER TABLE Payments ADD COLUMN Method VARCHAR(50) DEFAULT 'Cash'");
    } catch (Exception $e2) {}
    try {
        $pdo->exec("ALTER TABLE Payments ADD COLUMN Status VARCHAR(20) DEFAULT 'Pending'");
    } catch (Exception $e2) {}
    try {
        $pdo->exec("ALTER TABLE Payments ADD COLUMN TransactionId VARCHAR(50)");
    } catch (Exception $e2) {}
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php?redirect=checkout.php");
    exit();
}

$error = '';
$success = '';
$cartItems = [];
$totalAmount = 0;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $firstName = trim($_POST['first_name'] ?? '');
    $lastName = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $paymentMethod = $_POST['payment_method'] ?? '';
    $cartData = $_POST['cart_data'] ?? '';
    
    // Validate required fields
    if (empty($firstName) || empty($lastName) || empty($email) || empty($phone) || empty($paymentMethod) || empty($cartData)) {
        $error = "All fields are required.";
    } else {
        try {
            $cartItems = json_decode($cartData, true);
            
            if (!$cartItems || !is_array($cartItems)) {
                throw new Exception("Invalid cart data.");
            }
            
            // Calculate total
            $totalAmount = 0;
            foreach ($cartItems as $item) {
                $totalAmount += ($item['price'] ?? 0) * ($item['quantity'] ?? 1);
            }
            
            if ($totalAmount <= 0) {
                throw new Exception("Invalid cart total.");
            }
            
            // COMPACT BUT UNIQUE ID GENERATION - NO MORE TRUNCATION
            $maxRetries = 15;
            $bookingId = null;
            $paymentId = null;

            for ($i = 0; $i < $maxRetries; $i++) {
                // Generate compact but highly unique IDs
                $timestamp = date("ymdHis"); // Shorter year format
                $microtime = substr(str_replace(".", "", microtime(true)), -6); // Last 6 digits of microtime
                $randomBytes = substr(bin2hex(random_bytes(4)), 0, 8); // 8 hex chars
                $processId = getmypid() % 10000; // 4 digits max
                $randomSalt = rand(1000, 9999); // 4 digits

                // Create compact unique IDs (about 30-35 characters)
                $bookingId = "BKG_" . $timestamp . "_" . $microtime . "_" . $randomBytes . "_" . $processId . "_" . $randomSalt;
                $paymentId = "PAY_" . $timestamp . "_" . $microtime . "_" . $randomBytes . "_" . $processId . "_" . $randomSalt;

                // Check uniqueness in database
                $checkBooking = $pdo->prepare("SELECT COUNT(*) FROM Bookings WHERE BookingId = ?");
                $checkBooking->execute([$bookingId]);

                $checkPayment = $pdo->prepare("SELECT COUNT(*) FROM Payments WHERE PaymentId = ?");
                $checkPayment->execute([$paymentId]);

                if ($checkBooking->fetchColumn() == 0 && $checkPayment->fetchColumn() == 0) {
                    // Log successful generation for debugging
                    error_log("BOOKING: Generated unique ID: $bookingId (length: " . strlen($bookingId) . ", attempt: " . ($i + 1) . ")");
                    break; // Both IDs are unique
                }

                // Log collision for debugging
                error_log("BOOKING: ID collision detected on attempt " . ($i + 1) . ": $bookingId");

                if ($i == $maxRetries - 1) {
                    throw new Exception("Unable to generate unique booking ID after $maxRetries attempts. Please try again in a moment.");
                }

                // Random delay between retries
                usleep(rand(100000, 300000)); // 100-300ms random delay
            }
            
            // Start transaction
            $pdo->beginTransaction();
            
            // Get EventId from first cart item (all items should be from same event)
            $eventId = null;
            if (!empty($cartItems) && isset($cartItems[0]['event_id'])) {
                $eventId = $cartItems[0]['event_id'];
            } else {
                // Fallback: try to get any approved event
                $eventStmt = $pdo->query("SELECT EventId FROM Events WHERE IsApproved = 1 LIMIT 1");
                $eventResult = $eventStmt->fetch();
                if ($eventResult) {
                    $eventId = $eventResult['EventId'];
                } else {
                    throw new Exception("No valid event found for booking");
                }
            }

            // Create booking record (with fallback for missing TotalAmount column)
            try {
                $bookingStmt = $pdo->prepare("
                    INSERT INTO Bookings (BookingId, UserId, EventId, BookingDate, PaymentStatus, TotalAmount)
                    VALUES (?, ?, ?, NOW(), 'Pending', ?)
                ");
                $bookingStmt->execute([$bookingId, $_SESSION['user_id'], $eventId, $totalAmount]);
            } catch (Exception $e) {
                // Fallback if TotalAmount column doesn't exist
                if (strpos($e->getMessage(), 'TotalAmount') !== false) {
                    $bookingStmt = $pdo->prepare("
                        INSERT INTO Bookings (BookingId, UserId, EventId, BookingDate, PaymentStatus)
                        VALUES (?, ?, ?, NOW(), 'Pending')
                    ");
                    $bookingStmt->execute([$bookingId, $_SESSION['user_id'], $eventId]);
                } else {
                    throw $e;
                }
            }
            
            // Create payment record (with fallback for missing columns)
            try {
                $paymentStmt = $pdo->prepare("
                    INSERT INTO Payments (PaymentId, BookingId, Amount, PaymentDate, Method, Status, TransactionId)
                    VALUES (?, ?, ?, NOW(), ?, 'Pending', ?)
                ");
                $transactionId = 'TXN' . date('YmdHis') . rand(1000, 9999);
                $paymentStmt->execute([$paymentId, $bookingId, $totalAmount, $paymentMethod, $transactionId]);
            } catch (Exception $e) {
                // Fallback for basic Payments table
                if (strpos($e->getMessage(), 'Method') !== false || strpos($e->getMessage(), 'Status') !== false || strpos($e->getMessage(), 'TransactionId') !== false) {
                    $paymentStmt = $pdo->prepare("
                        INSERT INTO Payments (PaymentId, BookingId, Amount, PaymentDate)
                        VALUES (?, ?, ?, NOW())
                    ");
                    $paymentStmt->execute([$paymentId, $bookingId, $totalAmount]);
                } else {
                    throw $e;
                }
            }
            
            // Update booking with payment info
            $updateBookingStmt = $pdo->prepare("
                UPDATE Bookings SET PaymentStatus = 'Completed' WHERE BookingId = ?
            ");
            $updateBookingStmt->execute([$bookingId]);
            
            // Update payment status (with fallback for missing Status column)
            try {
                $updatePaymentStmt = $pdo->prepare("
                    UPDATE Payments SET Status = 'Completed' WHERE PaymentId = ?
                ");
                $updatePaymentStmt->execute([$paymentId]);
            } catch (Exception $e) {
                // Skip if Status column doesn't exist
                if (strpos($e->getMessage(), 'Status') === false) {
                    throw $e;
                }
            }
            
            // Mark tickets as booked (if they exist)
            foreach ($cartItems as $item) {
                if (isset($item['id'])) {
                    try {
                        $ticketUpdateStmt = $pdo->prepare("
                            UPDATE Tickets SET Status = 'Booked' WHERE TicketId = ?
                        ");
                        $ticketUpdateStmt->execute([$item['id']]);
                    } catch (Exception $e) {
                        // Continue if ticket doesn't exist
                    }
                }
            }
            
            $pdo->commit();
            
            // Clear cart and redirect to success page
            $success = "Booking successful! Booking ID: $bookingId";
            header("Location: working_payment_success.php?booking_id=$bookingId");
            exit();
            
        } catch (Exception $e) {
            $pdo->rollback();
            $error = "Booking failed: " . $e->getMessage();
        }
    }
}

// Get user info for pre-filling form
try {
    $userStmt = $pdo->prepare("SELECT FirstName, LastName, Email FROM Users WHERE UserId = ?");
    $userStmt->execute([$_SESSION['user_id']]);
    $user = $userStmt->fetch();
} catch (Exception $e) {
    $user = ['FirstName' => '', 'LastName' => '', 'Email' => ''];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; }
        .checkout-form { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: #667eea; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .cart-summary { background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .cart-item { display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee; }
        .cart-item:last-child { border-bottom: none; }
        .total-section { background: #e3f2fd; padding: 20px; border-radius: 10px; margin-top: 20px; }
        .total-amount { font-size: 24px; font-weight: bold; color: #1976d2; text-align: center; }
        .btn { width: 100%; padding: 15px; border: none; border-radius: 8px; font-size: 18px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a6fd8; transform: translateY(-2px); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .warning { color: orange; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .payment-methods { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
        .payment-option { border: 2px solid #ddd; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; transition: all 0.3s; }
        .payment-option:hover { border-color: #667eea; }
        .payment-option.selected { border-color: #667eea; background: #e3f2fd; }
        .payment-option input { display: none; }
        @media (max-width: 768px) {
            .form-row { grid-template-columns: 1fr; }
            .payment-methods { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-credit-card"></i> Checkout</h1>
            <p>Complete your booking</p>
        </div>

        <?php if ($error): ?>
            <div class="error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <!-- Cart Summary -->
        <div class="cart-summary">
            <h3><i class="fas fa-shopping-cart"></i> Order Summary</h3>
            <div id="cartDisplay">
                <div class="warning">
                    <i class="fas fa-exclamation-triangle"></i> Loading cart items...
                </div>
            </div>
            <div class="total-section">
                <div class="total-amount">
                    Total: <span id="totalDisplay">0</span> ETB
                </div>
            </div>
        </div>

        <!-- Checkout Form -->
        <form method="POST" class="checkout-form" id="checkoutForm">
            <h3><i class="fas fa-user"></i> Customer Information</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="first_name">First Name *</label>
                    <input type="text" id="first_name" name="first_name" value="<?= htmlspecialchars($user['FirstName'] ?? '') ?>" required>
                </div>
                <div class="form-group">
                    <label for="last_name">Last Name *</label>
                    <input type="text" id="last_name" name="last_name" value="<?= htmlspecialchars($user['LastName'] ?? '') ?>" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($user['Email'] ?? '') ?>" required>
                </div>
                <div class="form-group">
                    <label for="phone">Phone Number *</label>
                    <input type="tel" id="phone" name="phone" placeholder="+251-9XX-XXX-XXX" required>
                </div>
            </div>

            <h3><i class="fas fa-credit-card"></i> Payment Method</h3>
            <div class="payment-methods">
                <label class="payment-option">
                    <input type="radio" name="payment_method" value="Telebirr" required>
                    <i class="fas fa-mobile-alt"></i><br>
                    <strong>Telebirr</strong><br>
                    <small>Mobile Payment</small>
                </label>
                <label class="payment-option">
                    <input type="radio" name="payment_method" value="CBE Birr" required>
                    <i class="fas fa-university"></i><br>
                    <strong>CBE Birr</strong><br>
                    <small>Bank Transfer</small>
                </label>
                <label class="payment-option">
                    <input type="radio" name="payment_method" value="Cash" required>
                    <i class="fas fa-money-bill"></i><br>
                    <strong>Cash</strong><br>
                    <small>Pay at Venue</small>
                </label>
            </div>

            <input type="hidden" name="cart_data" id="cartData">
            
            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                <i class="fas fa-lock"></i> Complete Booking
            </button>
        </form>

        <div style="text-align: center; margin-top: 20px;">
            <a href="Finalll_updated.php" style="color: #667eea; text-decoration: none;">
                <i class="fas fa-arrow-left"></i> Back to Events
            </a>
        </div>
    </div>

    <script>
        let cartItems = [];
        let totalAmount = 0;

        // Load cart from localStorage
        function loadCart() {
            console.log('Loading cart from localStorage...');

            const cartData = localStorage.getItem('cart');
            console.log('Raw cart data:', cartData);

            if (cartData) {
                try {
                    cartItems = JSON.parse(cartData);
                    console.log('Parsed cart items:', cartItems);

                    if (Array.isArray(cartItems) && cartItems.length > 0) {
                        displayCart();
                        updateTotal();
                        updateCartData();
                        console.log('Cart loaded successfully with', cartItems.length, 'items');
                    } else {
                        console.warn('Cart is empty or not an array');
                        showEmptyCart();
                    }
                } catch (e) {
                    console.error('Error parsing cart data:', e);
                    showEmptyCart();
                }
            } else {
                console.warn('No cart data found in localStorage');

                // Try to load from session cart as fallback
                console.log('Attempting to load from session cart...');
                fetch('get_session_cart.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.cart && data.cart.length > 0) {
                            console.log('Found session cart:', data.cart);
                            cartItems = data.cart;
                            localStorage.setItem('cart', JSON.stringify(cartItems));
                            displayCart();
                            updateTotal();
                            updateCartData();
                        } else {
                            showEmptyCart();
                        }
                    })
                    .catch(e => {
                        console.error('Error loading session cart:', e);
                        showEmptyCart();
                    });
            }
        }

        function displayCart() {
            const cartDisplay = document.getElementById('cartDisplay');

            if (!cartItems || cartItems.length === 0) {
                showEmptyCart();
                return;
            }

            let html = '';
            cartItems.forEach((item, index) => {
                const price = parseInt(item.price) || 0;
                const quantity = parseInt(item.quantity) || 1;
                const itemTotal = price * quantity;

                html += `
                    <div class="cart-item">
                        <div>
                            <strong>${item.title || 'Event Ticket'}</strong><br>
                            <small>
                                ${item.type || 'Regular'}
                                ${item.seat ? '- Seat ' + item.seat : ''}
                                ${item.venue ? ' at ' + item.venue : ''}
                            </small><br>
                            <small style="color: #666;">
                                ${price.toLocaleString()} ETB × ${quantity} = ${itemTotal.toLocaleString()} ETB
                            </small>
                        </div>
                        <div>
                            <strong style="color: #667eea;">${itemTotal.toLocaleString()} ETB</strong>
                        </div>
                    </div>
                `;
            });

            cartDisplay.innerHTML = html;
            console.log('Cart displayed with', cartItems.length, 'items');
        }

        function showEmptyCart() {
            document.getElementById('cartDisplay').innerHTML = `
                <div class="warning">
                    <i class="fas fa-shopping-cart"></i> Your cart is empty.
                    <br><a href="Finalll_updated.php">Browse Events</a>
                </div>
            `;
            document.getElementById('submitBtn').disabled = true;
        }

        function updateTotal() {
            totalAmount = cartItems.reduce((sum, item) => {
                return sum + ((item.price || 0) * (item.quantity || 1));
            }, 0);
            
            document.getElementById('totalDisplay').textContent = totalAmount.toLocaleString();
            document.getElementById('submitBtn').disabled = totalAmount <= 0;
        }

        function updateCartData() {
            document.getElementById('cartData').value = JSON.stringify(cartItems);
        }

        // Payment method selection
        document.querySelectorAll('.payment-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                document.querySelectorAll('.payment-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Add selected class to clicked option
                this.classList.add('selected');
                
                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                
                // Enable submit button if cart has items
                if (cartItems.length > 0) {
                    document.getElementById('submitBtn').disabled = false;
                }
            });
        });

        // Form submission
        document.getElementById('checkoutForm').addEventListener('submit', function(e) {
            if (cartItems.length === 0) {
                e.preventDefault();
                alert('Your cart is empty. Please add items before checkout.');
                return false;
            }
            
            if (totalAmount <= 0) {
                e.preventDefault();
                alert('Invalid cart total. Please refresh and try again.');
                return false;
            }
            
            // Show loading state
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            submitBtn.disabled = true;
        });

        // Load cart on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCart();
            
            // Auto-fill phone with Ethiopian format
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.startsWith('251')) {
                    value = '+' + value;
                } else if (value.startsWith('9') && value.length <= 9) {
                    value = '+251-' + value;
                }
                this.value = value;
            });
        });

        console.log('Checkout page loaded');
        console.log('Cart items:', cartItems);
    </script>
</body>
</html>
