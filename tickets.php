<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) || !$_SESSION['loggedin']) {
    header("Location: login.php");
    exit();
}

// Secure session
if (!secureSession()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Get user's tickets/bookings
try {
    $stmt = $pdo->prepare("
        SELECT b.BookingId, b.BookingDate, b.TotalAmount, b.PaymentStatus, b.BookingReference,
               e.EventId, e.Title as EventTitle, e.EventDate, e.Place, e.EventImage,
               p.PaymentId, p.PaymentMethod, p.PaymentDate, p.Amount as PaymentAmount,
               GROUP_CONCAT(DISTINCT t.TicketType SEPARATOR ', ') as TicketTypes,
               COUNT(DISTINCT t.TicketId) as TicketCount
        FROM Bookings b
        LEFT JOIN Events e ON b.EventId = e.EventId
        LEFT JOIN Payments p ON b.BookingId = p.BookingId
        LEFT JOIN Tickets t ON e.EventId = t.EventId
        WHERE b.UserId = ?
        GROUP BY b.BookingId
        ORDER BY b.BookingDate DESC
    ");
    $stmt->execute([$userId]);
    $bookings = $stmt->fetchAll();
} catch (Exception $e) {
    $error = "Error loading tickets: " . $e->getMessage();
    $bookings = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Tickets - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
        .logo-text h1 {
            color: white;
            font-size: 1.5em;
            margin-bottom: 2px;
        }
        .logo-text p {
            color: rgba(255,255,255,0.8);
            font-size: 0.9em;
        }
        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .nav-links a:hover, .nav-links a.active {
            color: #ffd700;
        }
        .tickets-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        .tickets-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .tickets-header h1 {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
        }
        .tickets-header p {
            color: #666;
            font-size: 1.1em;
        }
        .tickets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
        }
        .ticket-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .ticket-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        .ticket-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        .ticket-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #764ba2;
        }
        .ticket-id {
            font-size: 0.9em;
            opacity: 0.9;
            margin-bottom: 5px;
        }
        .event-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .event-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .event-date {
            font-size: 0.95em;
        }
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-completed {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        .ticket-body {
            padding: 25px;
        }
        .ticket-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .info-item i {
            color: #667eea;
            width: 20px;
        }
        .info-item span {
            font-weight: 500;
        }
        .ticket-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }
        .btn-secondary:hover {
            background: #e9ecef;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .empty-state i {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }
        .empty-state h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .empty-state p {
            color: #666;
            margin-bottom: 30px;
        }
        .qr-placeholder {
            width: 80px;
            height: 80px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }
        @media (max-width: 768px) {
            .tickets-grid {
                grid-template-columns: 1fr;
            }
            .ticket-info {
                grid-template-columns: 1fr;
            }
            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <img alt="Addis Tickets Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
            <ul class="nav-links">
                <li><a href="Finalll_updated.php">Home</a></li>
                <li><a href="profile.php">Profile</a></li>
                <li><a href="tickets.php" class="active">My Tickets</a></li>
                <li><a href="support.php">Support</a></li>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </div>
    </nav>

    <div class="tickets-container">
        <div class="tickets-header">
            <h1><i class="fas fa-ticket-alt"></i> My Tickets</h1>
            <p>View and manage your event bookings</p>
        </div>

        <?php if (empty($bookings)): ?>
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <h3>No Tickets Yet</h3>
                <p>You haven't booked any tickets yet. Browse our events and book your first ticket!</p>
                <a href="Finalll_updated.php" class="btn btn-primary">
                    <i class="fas fa-calendar"></i> Browse Events
                </a>
            </div>
        <?php else: ?>
            <div class="tickets-grid">
                <?php foreach ($bookings as $booking): ?>
                    <div class="ticket-card">
                        <div class="ticket-header">
                            <div class="ticket-id">Booking #<?= htmlspecialchars($booking['BookingId']) ?></div>
                            <div class="event-title"><?= htmlspecialchars($booking['EventTitle'] ?? 'Event') ?></div>
                            <div class="event-details">
                                <div class="event-date">
                                    <i class="fas fa-calendar"></i>
                                    <?= $booking['EventDate'] ? date('M d, Y H:i', strtotime($booking['EventDate'])) : 'TBA' ?>
                                </div>
                                <span class="status-badge status-<?= strtolower($booking['PaymentStatus'] ?? 'pending') ?>">
                                    <?= ucfirst($booking['PaymentStatus'] ?? 'Pending') ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="ticket-body">
                            <div class="qr-placeholder">
                                <i class="fas fa-qrcode" style="font-size: 2em; color: #ccc;"></i>
                            </div>
                            
                            <div class="ticket-info">
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span><?= htmlspecialchars($booking['Place'] ?? 'Venue TBA') ?></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span><?= number_format($booking['TotalAmount'] ?? 0, 2) ?> ETB</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-credit-card"></i>
                                    <span><?= htmlspecialchars($booking['PaymentMethod'] ?? 'N/A') ?></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-clock"></i>
                                    <span><?= date('M d, Y', strtotime($booking['BookingDate'])) ?></span>
                                </div>
                            </div>
                            
                            <div class="ticket-actions">
                                <a href="ticket_detail.php?booking_id=<?= $booking['BookingId'] ?>" class="btn btn-primary">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                <?php if ($booking['PaymentStatus'] === 'Completed'): ?>
                                    <a href="#" class="btn btn-secondary" onclick="downloadTicket('<?= $booking['BookingId'] ?>')">
                                        <i class="fas fa-download"></i> Download
                                    </a>
                                <?php elseif ($booking['PaymentStatus'] === 'Pending'): ?>
                                    <a href="working_checkout.php?booking_id=<?= $booking['BookingId'] ?>" class="btn btn-secondary">
                                        <i class="fas fa-credit-card"></i> Pay Now
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function downloadTicket(bookingId) {
            // Placeholder for download functionality
            alert('Download functionality will be implemented. Booking ID: ' + bookingId);
            // In a real implementation, this would generate and download a PDF ticket
        }
        
        function shareTicket(bookingId) {
            // Placeholder for share functionality
            if (navigator.share) {
                navigator.share({
                    title: 'My Event Ticket',
                    text: 'Check out my ticket for this event!',
                    url: window.location.origin + '/ticket/' + bookingId
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.origin + '/ticket/' + bookingId;
                navigator.clipboard.writeText(url).then(() => {
                    alert('Ticket link copied to clipboard!');
                });
            }
        }
    </script>
</body>
</html>
