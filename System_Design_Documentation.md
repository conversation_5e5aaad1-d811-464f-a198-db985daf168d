# System Design Documentation
## Event Booking System - Addis Tickets

---

## 4.1 Introduction

The Addis Tickets Event Booking System is a comprehensive web-based platform designed to facilitate event management and ticket booking in Addis Ababa, Ethiopia. The system serves as a centralized marketplace where event organizers can create and manage events, while users can discover, book, and purchase tickets for various events.

### System Overview
The platform operates as a multi-user system supporting three distinct user roles:
- **End Users**: Browse events, make bookings, and manage their tickets
- **Event Organizers**: Create, manage, and monitor their events and bookings
- **System Administrators**: Oversee the entire platform, manage users, and approve events

### Key Features
- Event creation and management with multimedia support
- Multi-tier ticket booking system (Regular, VIP, VVIP, Student, Group)
- Secure payment processing with multiple payment methods
- Real-time analytics and reporting
- Email verification and notification system
- Comprehensive admin dashboard with system controls
- Mobile-responsive design for cross-platform accessibility

---

## 4.2 Design Goals

### Primary Goals

#### 4.2.1 Scalability
- **Horizontal Scaling**: Database design supports partitioning and replication
- **Performance Optimization**: Indexed database queries and optimized SQL views
- **Load Distribution**: Stateless session management for load balancer compatibility
- **Caching Strategy**: Session-based caching for frequently accessed data

#### 4.2.2 Security
- **Data Protection**: Password hashing using bcrypt with salt
- **Input Validation**: Comprehensive sanitization and validation functions
- **CSRF Protection**: Token-based cross-site request forgery prevention
- **SQL Injection Prevention**: Prepared statements throughout the application
- **Session Security**: Secure session management with timeout and regeneration

#### 4.2.3 Usability
- **Intuitive Interface**: Modern, responsive UI with clear navigation
- **Accessibility**: WCAG-compliant design elements
- **Multi-language Support**: UTF-8 encoding for Ethiopian languages
- **Mobile Optimization**: Responsive design for mobile and tablet devices

#### 4.2.4 Reliability
- **Error Handling**: Comprehensive exception handling and logging
- **Data Integrity**: Foreign key constraints and transaction management
- **Backup Strategy**: Database backup and recovery procedures
- **Monitoring**: System health monitoring and alerting

#### 4.2.5 Maintainability
- **Modular Architecture**: Separation of concerns with distinct modules
- **Code Documentation**: Inline comments and API documentation
- **Version Control**: Git-based version management
- **Testing Framework**: Unit and integration testing capabilities

---

## 4.3 Design Trade-offs

### 4.3.1 Performance vs. Consistency
**Decision**: Chose strong consistency over eventual consistency
- **Rationale**: Financial transactions require immediate consistency
- **Trade-off**: Slightly higher latency for critical operations
- **Mitigation**: Optimized queries and database indexing

### 4.3.2 Security vs. User Experience
**Decision**: Implemented multi-factor authentication for sensitive operations
- **Rationale**: Financial and personal data protection is paramount
- **Trade-off**: Additional steps in user workflow
- **Mitigation**: Progressive security (lighter checks for browsing, stronger for transactions)

### 4.3.3 Feature Richness vs. Simplicity
**Decision**: Comprehensive feature set with optional advanced features
- **Rationale**: Competitive advantage through feature completeness
- **Trade-off**: Increased complexity and learning curve
- **Mitigation**: Progressive disclosure and user onboarding

### 4.3.4 Real-time Updates vs. System Resources
**Decision**: Polling-based updates instead of WebSocket connections
- **Rationale**: Simpler implementation and better resource management
- **Trade-off**: Less real-time responsiveness
- **Mitigation**: Optimized polling intervals and caching

---

## 4.4 Subsystem Decomposition

### 4.4.1 User Management Subsystem
**Components**:
- User Registration and Authentication
- Profile Management
- Email Verification System
- Password Reset Functionality

**Responsibilities**:
- User account lifecycle management
- Authentication and authorization
- Profile data maintenance
- Security credential management

### 4.4.2 Event Management Subsystem
**Components**:
- Event Creation and Editing
- Event Approval Workflow
- Event Search and Discovery
- Event Analytics and Reporting

**Responsibilities**:
- Event lifecycle management
- Content moderation and approval
- Search and filtering capabilities
- Performance metrics and analytics

### 4.4.3 Booking and Payment Subsystem
**Components**:
- Ticket Selection and Reservation
- Payment Processing
- Booking Confirmation
- Refund Management

**Responsibilities**:
- Inventory management
- Payment transaction processing
- Booking state management
- Financial reconciliation

### 4.4.4 Administrative Subsystem
**Components**:
- System Configuration
- User and Organizer Management
- Analytics Dashboard
- Support Ticket System
- Feedback Management System

**Responsibilities**:
- System-wide configuration management
- User role and permission management
- Business intelligence and reporting
- Customer support operations
- User feedback collection and analysis

### 4.4.5 Communication Subsystem
**Components**:
- Email Notification System
- SMS Integration (planned)
- In-app Messaging
- Support Ticket Communication

**Responsibilities**:
- Multi-channel communication
- Notification delivery
- Customer support communication
- System alerts and notifications

---

## 4.5 Design Phase Models

### 4.5.1 Class Modeling

#### Core Entity Classes

**User Class**
```
Class: User
Attributes:
- UserId: CHAR(9) [Primary Key]
- FirstName: VARCHAR(50)
- LastName: VARCHAR(50)
- Gender: ENUM('Male', 'Female')
- DateOfBirth: DATE
- PhoneNumber: VARCHAR(20)
- Email: VARCHAR(100) [Unique]
- PasswordHash: VARCHAR(255)
- HouseNumber: INT
- SubCity: VARCHAR(50)
- City: VARCHAR(50)
- EmailVerified: BOOLEAN
- EmailVerificationToken: VARCHAR(64)
- TokenExpiry: DATETIME
- RegistrationDate: TIMESTAMP
- LastLoginDate: TIMESTAMP
- IsActive: BOOLEAN
- ProfilePicture: VARCHAR(255)

Methods:
+ register()
+ login()
+ updateProfile()
+ verifyEmail()
+ resetPassword()
+ deactivateAccount()
```

**Event Class**
```
Class: Event
Attributes:
- EventId: CHAR(9) [Primary Key]
- Title: VARCHAR(200)
- Description: TEXT
- EventDate: DATETIME
- EndDate: DATETIME
- Place: VARCHAR(200)
- Address: TEXT
- EventCategory: VARCHAR(50)
- MaxCapacity: INT
- TicketPrice: DECIMAL(10,2)
- EventImage: VARCHAR(255)
- OrganizerId: CHAR(9) [Foreign Key]
- IsApproved: BOOLEAN
- IsFeatured: BOOLEAN
- CreatedDate: TIMESTAMP
- UpdatedDate: TIMESTAMP

Methods:
+ createEvent()
+ updateEvent()
+ deleteEvent()
+ approveEvent()
+ getEventDetails()
+ getBookingStatistics()
```

**Booking Class**
```
Class: Booking
Attributes:
- BookingId: CHAR(9) [Primary Key]
- UserId: CHAR(9) [Foreign Key]
- EventId: CHAR(9) [Foreign Key]
- TicketId: CHAR(9) [Foreign Key]
- Quantity: INT
- TotalAmount: DECIMAL(10,2)
- BookingDate: TIMESTAMP
- PaymentStatus: ENUM('Pending', 'Completed', 'Failed', 'Cancelled', 'Refunded')
- BookingStatus: ENUM('Active', 'Cancelled', 'Expired')
- CustomerName: VARCHAR(100)
- CustomerEmail: VARCHAR(100)
- CustomerPhone: VARCHAR(20)
- SpecialRequests: TEXT
- BookingReference: VARCHAR(20)
- ExpiryDate: DATETIME

Methods:
+ createBooking()
+ updateBookingStatus()
+ processPayment()
+ cancelBooking()
+ generateReference()
+ sendConfirmation()
```

**EventOrganizer Class**
```
Class: EventOrganizer
Attributes:
- OrganizerId: CHAR(9) [Primary Key]
- OrganizerName: VARCHAR(100)
- Email: VARCHAR(100) [Unique]
- PasswordHash: VARCHAR(255)
- PhoneNumber: VARCHAR(20)
- Address: TEXT
- City: VARCHAR(50)
- IsApproved: BOOLEAN
- CreatedDate: TIMESTAMP
- UpdatedDate: TIMESTAMP

Methods:
+ registerOrganizer()
+ loginOrganizer()
+ createEvent()
+ manageEvents()
+ viewAnalytics()
+ updateProfile()
```

#### Relationship Classes

**Ticket Class**
```
Class: Ticket
Attributes:
- TicketId: CHAR(9) [Primary Key]
- EventId: CHAR(9) [Foreign Key]
- SeatNumber: VARCHAR(10)
- SeatRow: VARCHAR(5)
- SeatSection: VARCHAR(20)
- Status: ENUM('Available', 'Reserved', 'Sold', 'Cancelled')
- TicketType: ENUM('VIP', 'Regular', 'VVIP', 'Student', 'Group')
- Price: DECIMAL(10,2)
- AvailableTickets: INT
- CreatedDate: TIMESTAMP
- UpdatedDate: TIMESTAMP

Methods:
+ reserveTicket()
+ releaseTicket()
+ updatePrice()
+ checkAvailability()
+ generateTicket()
```

**Payment Class**
```
Class: Payment
Attributes:
- PaymentId: CHAR(9) [Primary Key]
- BookingId: CHAR(9) [Foreign Key]
- Amount: DECIMAL(10,2)
- PaymentMethod: ENUM('Cash', 'Card', 'Bank Transfer', 'Mobile Money', 'Online')
- PaymentStatus: ENUM('Pending', 'Completed', 'Failed', 'Cancelled', 'Refunded')
- PaymentDate: TIMESTAMP
- TransactionId: VARCHAR(100)
- PaymentGateway: VARCHAR(50)
- PaymentReference: VARCHAR(100)
- ProcessedBy: CHAR(9)
- Notes: TEXT

Methods:
+ processPayment()
+ refundPayment()
+ updateStatus()
+ generateReceipt()
+ validateTransaction()
```

**SupportTicket Class**
```
Class: SupportTicket
Attributes:
- TicketId: CHAR(9) [Primary Key]
- UserId: CHAR(9) [Foreign Key]
- Subject: VARCHAR(200)
- Message: TEXT
- Category: ENUM('Technical', 'Booking', 'Payment', 'General', 'Complaint', 'Feedback')
- Priority: ENUM('Low', 'Medium', 'High', 'Urgent')
- Status: ENUM('Open', 'In Progress', 'Resolved', 'Closed')
- AssignedTo: CHAR(9) [Foreign Key to Admin]
- CreatedDate: TIMESTAMP
- UpdatedDate: TIMESTAMP
- ResolvedDate: TIMESTAMP

Methods:
+ createTicket()
+ updateStatus()
+ assignToAdmin()
+ addResponse()
+ closeTicket()
+ escalatePriority()
```

### 4.5.2 Persistent Model

#### ******* Mapping Class Diagram to Relations

**Primary Entity Tables**:

1. **Users Table**
   - Maps User class attributes directly
   - Primary Key: UserId (CHAR(9))
   - Unique constraints: Email
   - Indexes: RegistrationDate, Email

2. **Events Table**
   - Maps Event class attributes
   - Primary Key: EventId (CHAR(9))
   - Foreign Key: OrganizerId → EventOrganizers(OrganizerId)
   - Indexes: EventDate, EventCategory, IsApproved

3. **EventOrganizers Table**
   - Maps EventOrganizer class attributes
   - Primary Key: OrganizerId (CHAR(9))
   - Unique constraints: Email
   - Indexes: IsApproved, CreatedDate

4. **Bookings Table**
   - Maps Booking class attributes
   - Primary Key: BookingId (CHAR(9))
   - Foreign Keys: UserId, EventId, TicketId
   - Unique constraints: BookingReference
   - Indexes: BookingDate, PaymentStatus, UserId, EventId

5. **Tickets Table**
   - Maps Ticket class attributes
   - Primary Key: TicketId (CHAR(9))
   - Foreign Key: EventId → Events(EventId)
   - Indexes: EventId + Status, TicketType

6. **Payments Table**
   - Maps Payment class attributes
   - Primary Key: PaymentId (CHAR(9))
   - Foreign Key: BookingId → Bookings(BookingId)
   - Indexes: PaymentDate, PaymentStatus, PaymentMethod

**Supporting Tables**:

7. **Admins Table**
   - Administrative user management
   - Primary Key: AdminId (CHAR(9))
   - Foreign Keys: UserId, OrganizerId (optional)

8. **SystemSettings Table**
   - Configuration management
   - Primary Key: SettingKey (VARCHAR(100))
   - Supports JSON data types for complex settings

9. **SupportTickets Table**
   - Customer support and feedback system
   - Primary Key: TicketId (CHAR(9))
   - Foreign Keys: UserId, AssignedTo (AdminId)
   - Indexes: Status, Priority, Category, CreatedDate
   - Supports feedback collection with rating extraction

10. **AuditLog Table**
    - System activity tracking
    - Primary Key: LogId (AUTO_INCREMENT)
    - JSON fields for old/new values

#### ******* Normalization

**First Normal Form (1NF)**:
- All tables have atomic values
- No repeating groups
- Each column contains single values
- Primary keys defined for all tables

**Second Normal Form (2NF)**:
- All non-key attributes fully dependent on primary key
- Separate tables for Events, Users, Bookings eliminate partial dependencies
- Junction tables (Bookings) properly link many-to-many relationships

**Third Normal Form (3NF)**:
- No transitive dependencies
- EventOrganizers separated from Events
- Payment information separated from Bookings
- User profile data consolidated in Users table

**Denormalization Decisions**:
- **EventAnalytics View**: Denormalized for performance
  - Aggregates booking counts and revenue
  - Reduces join operations for reporting
- **UserAnalytics View**: Denormalized user statistics
  - Pre-calculated user engagement metrics
- **RevenueAnalytics View**: Financial reporting optimization
  - Daily revenue aggregations by payment method

**Indexing Strategy**:
```sql
-- Performance-critical indexes
CREATE INDEX idx_events_category_date ON Events(EventCategory, EventDate);
CREATE INDEX idx_bookings_date_status ON Bookings(BookingDate, PaymentStatus);
CREATE INDEX idx_payments_date_status ON Payments(PaymentDate, PaymentStatus);
CREATE INDEX idx_users_registration_date ON Users(RegistrationDate);

-- Foreign key indexes (automatically created)
CREATE INDEX idx_bookings_user ON Bookings(UserId);
CREATE INDEX idx_bookings_event ON Bookings(EventId);
CREATE INDEX idx_tickets_event ON Tickets(EventId);
```

### 4.5.3 User Interface Design

#### ******* Design Principles

**Modern Material Design**:
- Clean, minimalist interface with intuitive navigation
- Consistent color scheme: Primary (#667eea), Secondary (#764ba2)
- Responsive grid system for cross-device compatibility
- Accessibility-first approach with WCAG 2.1 compliance

**User Experience Strategy**:
- Progressive disclosure of information
- Context-aware navigation and actions
- Real-time feedback for user actions
- Mobile-first responsive design

#### ******* Interface Architecture

**Navigation Structure**:
```
Main Navigation
├── Home (Event Discovery)
├── Events
│   ├── Browse Events
│   ├── Search & Filter
│   └── Event Categories
├── My Account
│   ├── Profile Management
│   ├── Booking History
│   ├── Tickets
│   └── Settings
└── Support
    ├── Help Center
    ├── Contact Support
    └── FAQ
```

**User Role-Specific Interfaces**:

1. **End User Interface**:
   - Event discovery and search
   - Ticket booking workflow
   - Personal dashboard with booking history
   - Profile management

2. **Event Organizer Interface**:
   - Event creation and management
   - Analytics dashboard
   - Booking management
   - Revenue tracking

3. **Administrator Interface**:
   - System-wide analytics
   - User and organizer management
   - Event approval workflow
   - Feedback management and analysis
   - System configuration

#### 4.5.3.3 Key Interface Components

**Event Discovery Page**:
- Hero section with featured events
- Search and filter functionality
- Category-based browsing
- Event cards with key information
- Pagination and infinite scroll

**Event Details Page**:
- Comprehensive event information
- Interactive seating chart (when applicable)
- Ticket type selection
- Social sharing capabilities
- Related events recommendations

**Booking Workflow**:
1. Ticket selection with real-time availability
2. Customer information collection
3. Payment method selection
4. Booking confirmation and receipt
5. Email confirmation with QR codes

**Dashboard Interfaces**:
- Real-time statistics and KPIs
- Interactive charts and graphs
- Quick action buttons
- Recent activity feeds
- Notification center

#### ******* Responsive Design Strategy

**Breakpoint System**:
- Mobile: 320px - 768px
- Tablet: 768px - 1024px
- Desktop: 1024px - 1440px
- Large Desktop: 1440px+

**Mobile Optimizations**:
- Touch-friendly interface elements
- Simplified navigation with hamburger menu
- Optimized forms with appropriate input types
- Swipe gestures for image galleries
- Progressive web app capabilities

### 4.5.4 Deployment Diagram

#### ******* Production Environment Architecture

```
[Internet]
    ↓
[Load Balancer/Reverse Proxy - Nginx]
    ↓
[Web Server Cluster]
├── [Web Server 1 - Apache/PHP 8.1]
├── [Web Server 2 - Apache/PHP 8.1]
└── [Web Server 3 - Apache/PHP 8.1]
    ↓
[Database Cluster]
├── [Primary MySQL Server 8.0]
├── [Read Replica 1]
└── [Read Replica 2]
    ↓
[File Storage]
├── [Local Storage - Event Images]
└── [CDN - Static Assets]
```

#### ******* Component Distribution

**Web Tier**:
- **Technology**: Apache HTTP Server 2.4 with PHP 8.1
- **Configuration**: mod_php with OPcache enabled
- **Session Management**: File-based sessions with Redis backup
- **SSL/TLS**: Let's Encrypt certificates with automatic renewal

**Application Tier**:
- **Framework**: Custom PHP application with MVC pattern
- **Security**: CSRF protection, input validation, SQL injection prevention
- **Caching**: APCu for opcode caching, Redis for session storage
- **Logging**: Centralized logging with log rotation

**Database Tier**:
- **Primary Database**: MySQL 8.0 with InnoDB storage engine
- **Read Replicas**: 2 read-only replicas for query distribution
- **Backup Strategy**: Daily full backups, hourly incremental backups
- **Monitoring**: MySQL Performance Schema and slow query logging

**File Storage**:
- **Local Storage**: Event images and user uploads
- **CDN Integration**: CloudFlare for static asset delivery
- **Backup**: Automated file backup to cloud storage

#### ******* Deployment Configuration

**Server Specifications**:
- **Web Servers**: 4 CPU cores, 8GB RAM, 100GB SSD
- **Database Server**: 8 CPU cores, 32GB RAM, 500GB SSD
- **Load Balancer**: 2 CPU cores, 4GB RAM, 50GB SSD

**Network Configuration**:
- **Internal Network**: Private VLAN for server communication
- **External Access**: Public IP with firewall protection
- **SSL Termination**: At load balancer level
- **Database Access**: Private network only

**Monitoring and Alerting**:
- **System Monitoring**: Nagios for infrastructure monitoring
- **Application Monitoring**: Custom health checks and error logging
- **Performance Monitoring**: New Relic for application performance
- **Alerting**: Email and SMS notifications for critical issues

### 4.5.5 Network Design

#### ******* Network Architecture

**Three-Tier Network Architecture**:

```
[Internet Gateway]
    ↓
[DMZ - Demilitarized Zone]
├── [Load Balancer - ********/24]
├── [Web Servers - ********/24]
└── [Reverse Proxy - ********/24]
    ↓
[Application Tier - ********/24]
├── [Application Servers]
├── [Session Storage - Redis]
└── [File Storage Servers]
    ↓
[Database Tier - ********/24]
├── [Primary Database Server]
├── [Read Replica Servers]
└── [Backup Storage]
```

#### ******* Security Zones

**Public Zone (DMZ)**:
- **Purpose**: External-facing services
- **Components**: Load balancer, web servers, reverse proxy
- **Security**: WAF (Web Application Firewall), DDoS protection
- **Access**: Internet → DMZ (ports 80, 443 only)

**Application Zone**:
- **Purpose**: Application logic and session management
- **Components**: PHP application servers, Redis cache
- **Security**: Internal firewall, application-level security
- **Access**: DMZ → Application Zone (restricted ports)

**Database Zone**:
- **Purpose**: Data storage and management
- **Components**: MySQL servers, backup systems
- **Security**: Database firewall, encrypted connections
- **Access**: Application Zone → Database Zone (port 3306 only)

#### ******* Network Security

**Firewall Rules**:
```
# External to DMZ
ALLOW Internet → DMZ:80,443 (HTTP/HTTPS)
DENY Internet → DMZ:* (All other ports)

# DMZ to Application
ALLOW DMZ → App:8080 (Application servers)
ALLOW DMZ → App:6379 (Redis cache)
DENY DMZ → App:* (All other ports)

# Application to Database
ALLOW App → DB:3306 (MySQL)
ALLOW App → DB:22 (SSH for management)
DENY App → DB:* (All other ports)
```

**SSL/TLS Configuration**:
- **External Traffic**: TLS 1.3 with strong cipher suites
- **Internal Traffic**: TLS 1.2 for database connections
- **Certificate Management**: Automated certificate renewal
- **HSTS**: HTTP Strict Transport Security enabled

**VPN Access**:
- **Administrative Access**: Site-to-site VPN for remote management
- **Developer Access**: Client VPN for development and debugging
- **Monitoring Access**: Dedicated VPN for monitoring systems

#### ******* Performance Optimization

**Content Delivery Network (CDN)**:
- **Static Assets**: CSS, JavaScript, images served via CDN
- **Geographic Distribution**: Multiple edge locations
- **Caching Strategy**: Long-term caching for static content
- **Compression**: Gzip compression for text-based assets

**Load Balancing Strategy**:
- **Algorithm**: Round-robin with health checks
- **Session Affinity**: Sticky sessions for user consistency
- **Failover**: Automatic failover to healthy servers
- **Health Monitoring**: Regular health checks every 30 seconds

**Database Optimization**:
- **Read/Write Splitting**: Writes to primary, reads from replicas
- **Connection Pooling**: Persistent connections to reduce overhead
- **Query Optimization**: Indexed queries and query caching
- **Replication Lag Monitoring**: Real-time replication monitoring

---

## Conclusion

This system design documentation provides a comprehensive overview of the Addis Tickets Event Booking System architecture. The design emphasizes scalability, security, and maintainability while delivering a user-friendly experience across all user roles. The modular architecture allows for future enhancements and integrations while maintaining system stability and performance.

The implementation follows industry best practices for web application development, database design, and network security. Regular reviews and updates of this documentation ensure alignment with evolving requirements and technological advances.
