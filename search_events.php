<?php
session_start();
require_once 'db_connect.php';

// Get search parameters
$searchQuery = $_GET['q'] ?? '';
$category = $_GET['category'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$priceMin = $_GET['price_min'] ?? '';
$priceMax = $_GET['price_max'] ?? '';
$sortBy = $_GET['sort'] ?? 'date_asc';

// Sanitize inputs
$searchQuery = filter_var($searchQuery, FILTER_SANITIZE_STRING);
$category = filter_var($category, FILTER_SANITIZE_STRING);
$dateFrom = filter_var($dateFrom, FILTER_SANITIZE_STRING);
$dateTo = filter_var($dateTo, FILTER_SANITIZE_STRING);
$priceMin = filter_var($priceMin, FILTER_VALIDATE_FLOAT);
$priceMax = filter_var($priceMax, FILTER_VALIDATE_FLOAT);
$sortBy = filter_var($sortBy, FILTER_SANITIZE_STRING);

try {
    // Build the query
    $sql = "SELECT e.*, o.OrganizerName, MIN(t.Price) as MinPrice, MAX(t.Price) as MaxPrice
            FROM Events e
            LEFT JOIN EventOrganizers o ON e.OrganizerId = o.OrganizerId
            LEFT JOIN Tickets t ON e.EventId = t.EventId
            WHERE e.IsApproved = 1";
    
    $params = [];
    
    // Add search conditions
    if (!empty($searchQuery)) {
        $sql .= " AND (e.Title LIKE ? OR e.Description LIKE ? OR e.Place LIKE ?)";
        $searchTerm = "%$searchQuery%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($category)) {
        $sql .= " AND e.EventCategory = ?";
        $params[] = $category;
    }
    
    if (!empty($dateFrom)) {
        $sql .= " AND e.EventDate >= ?";
        $params[] = $dateFrom;
    }
    
    if (!empty($dateTo)) {
        $sql .= " AND e.EventDate <= ?";
        $params[] = $dateTo;
    }
    
    $sql .= " GROUP BY e.EventId";
    
    // Add price filtering after GROUP BY
    if ($priceMin !== false && $priceMin !== null) {
        $sql .= " HAVING MinPrice >= ?";
        $params[] = $priceMin;
    }
    
    if ($priceMax !== false && $priceMax !== null) {
        if ($priceMin !== false && $priceMin !== null) {
            $sql .= " AND MaxPrice <= ?";
        } else {
            $sql .= " HAVING MaxPrice <= ?";
        }
        $params[] = $priceMax;
    }
    
    // Add sorting
    switch ($sortBy) {
        case 'date_desc':
            $sql .= " ORDER BY e.EventDate DESC";
            break;
        case 'price_asc':
            $sql .= " ORDER BY MinPrice ASC";
            break;
        case 'price_desc':
            $sql .= " ORDER BY MaxPrice DESC";
            break;
        case 'title_asc':
            $sql .= " ORDER BY e.Title ASC";
            break;
        case 'title_desc':
            $sql .= " ORDER BY e.Title DESC";
            break;
        default:
            $sql .= " ORDER BY e.EventDate ASC";
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $events = $stmt->fetchAll();
    
    // Get available categories for filter
    $categoryStmt = $pdo->prepare("SELECT DISTINCT EventCategory FROM Events WHERE IsApproved = 1 ORDER BY EventCategory");
    $categoryStmt->execute();
    $categories = $categoryStmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Search error: " . $e->getMessage();
    $events = [];
    $categories = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Events - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .search-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .search-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .search-filters { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .filter-group { display: flex; flex-direction: column; }
        .filter-group label { margin-bottom: 5px; font-weight: 600; }
        .filter-group input, .filter-group select { padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .search-results { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 25px; }
        .event-card { background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease; }
        .event-card:hover { transform: translateY(-5px); }
        .event-image { height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; }
        .event-image img { width: 100%; height: 100%; object-fit: cover; }
        .event-details { padding: 20px; }
        .event-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; }
        .event-info { margin-bottom: 15px; }
        .event-info div { display: flex; align-items: center; margin-bottom: 8px; color: #666; }
        .event-info i { margin-right: 8px; width: 16px; }
        .event-price { font-size: 18px; font-weight: bold; color: #667eea; margin-bottom: 15px; }
        .btn { padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; text-align: center; }
        .btn:hover { background: #5a6fd8; }
        .results-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .results-count { color: #666; }
        .sort-select { padding: 8px; border: 1px solid #ddd; border-radius: 5px; }
        .no-results { text-align: center; padding: 60px 20px; }
        .no-results i { font-size: 64px; color: #ddd; margin-bottom: 20px; }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img alt="Addis Ababa Stadium Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
            <ul class="nav-links">
                <li><a href="Finalll_updated.php">Home</a></li>
                <li><a href="search_events.php" class="active">Search Events</a></li>
                <?php if (isset($_SESSION['user_id'])): ?>
                    <li><a href="profile.php">Profile</a></li>
                    <li><a href="tickets.php">My Tickets</a></li>
                    <li><a href="logout.php">Logout</a></li>
                <?php else: ?>
                    <li><a href="login.php">Login</a></li>
                    <li><a href="register.php">Register</a></li>
                <?php endif; ?>
            </ul>
        </nav>
    </header>

    <div class="search-container">
        <div class="search-header">
            <h1><i class="fas fa-search"></i> Search Events</h1>
            <p>Find the perfect event for you</p>
        </div>

        <!-- Search Filters -->
        <div class="search-filters">
            <form method="GET" action="search_events.php">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="q">Search Keywords</label>
                        <input type="text" id="q" name="q" placeholder="Event name, description..." value="<?= htmlspecialchars($searchQuery) ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="category">Category</label>
                        <select id="category" name="category">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?= htmlspecialchars($cat['EventCategory']) ?>" 
                                        <?= $category === $cat['EventCategory'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($cat['EventCategory']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="date_from">Date From</label>
                        <input type="date" id="date_from" name="date_from" value="<?= htmlspecialchars($dateFrom) ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="date_to">Date To</label>
                        <input type="date" id="date_to" name="date_to" value="<?= htmlspecialchars($dateTo) ?>">
                    </div>
                </div>
                
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="price_min">Min Price (ETB)</label>
                        <input type="number" id="price_min" name="price_min" placeholder="0" value="<?= htmlspecialchars($priceMin) ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="price_max">Max Price (ETB)</label>
                        <input type="number" id="price_max" name="price_max" placeholder="1000" value="<?= htmlspecialchars($priceMax) ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label for="sort">Sort By</label>
                        <select id="sort" name="sort">
                            <option value="date_asc" <?= $sortBy === 'date_asc' ? 'selected' : '' ?>>Date (Earliest First)</option>
                            <option value="date_desc" <?= $sortBy === 'date_desc' ? 'selected' : '' ?>>Date (Latest First)</option>
                            <option value="price_asc" <?= $sortBy === 'price_asc' ? 'selected' : '' ?>>Price (Low to High)</option>
                            <option value="price_desc" <?= $sortBy === 'price_desc' ? 'selected' : '' ?>>Price (High to Low)</option>
                            <option value="title_asc" <?= $sortBy === 'title_asc' ? 'selected' : '' ?>>Title (A-Z)</option>
                            <option value="title_desc" <?= $sortBy === 'title_desc' ? 'selected' : '' ?>>Title (Z-A)</option>
                        </select>
                    </div>
                    
                    <div class="filter-group" style="align-self: end;">
                        <button type="submit" class="btn">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search Results -->
        <div class="results-header">
            <div class="results-count">
                <?= count($events) ?> event(s) found
                <?php if (!empty($searchQuery)): ?>
                    for "<?= htmlspecialchars($searchQuery) ?>"
                <?php endif; ?>
            </div>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <?php if (empty($events)): ?>
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h2>No Events Found</h2>
                <p>Try adjusting your search criteria or browse all events.</p>
                <a href="Finalll_updated.php" class="btn">Browse All Events</a>
            </div>
        <?php else: ?>
            <div class="search-results">
                <?php foreach ($events as $event): ?>
                    <div class="event-card">
                        <div class="event-image">
                            <?php if (!empty($event['ImagePath']) && file_exists($event['ImagePath'])): ?>
                                <img src="<?= htmlspecialchars($event['ImagePath']) ?>" alt="<?= htmlspecialchars($event['Title']) ?>">
                            <?php else: ?>
                                <i class="fas fa-calendar-alt" style="font-size: 48px; color: #ddd;"></i>
                            <?php endif; ?>
                        </div>
                        
                        <div class="event-details">
                            <div class="event-title"><?= htmlspecialchars($event['Title']) ?></div>
                            
                            <div class="event-info">
                                <div>
                                    <i class="fas fa-calendar"></i>
                                    <?= date('M j, Y \a\t g:i A', strtotime($event['EventDate'])) ?>
                                </div>
                                <div>
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?= htmlspecialchars($event['Place']) ?>
                                </div>
                                <div>
                                    <i class="fas fa-user"></i>
                                    <?= htmlspecialchars($event['OrganizerName']) ?>
                                </div>
                                <div>
                                    <i class="fas fa-tag"></i>
                                    <?= htmlspecialchars($event['EventCategory']) ?>
                                </div>
                            </div>
                            
                            <div class="event-price">
                                <?php if ($event['MinPrice'] && $event['MaxPrice']): ?>
                                    <?php if ($event['MinPrice'] == $event['MaxPrice']): ?>
                                        <?= number_format($event['MinPrice'], 2) ?> ETB
                                    <?php else: ?>
                                        <?= number_format($event['MinPrice'], 2) ?> - <?= number_format($event['MaxPrice'], 2) ?> ETB
                                    <?php endif; ?>
                                <?php else: ?>
                                    Price TBA
                                <?php endif; ?>
                            </div>
                            
                            <a href="event_details.php?id=<?= $event['EventId'] ?>" class="btn">
                                <i class="fas fa-info-circle"></i> View Details
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
