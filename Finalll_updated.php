<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
$isLoggedIn = isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true;

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "eventbb";

$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get platform statistics
$statsQuery = "
    SELECT
        (SELECT COUNT(*) FROM Events WHERE IsApproved = 1) as total_events,
        (SELECT COUNT(*) FROM Users) as total_users,
        (SELECT COUNT(*) FROM Bookings WHERE PaymentStatus = 'Completed') as total_bookings,
        (SELECT COUNT(*) FROM Events WHERE IsApproved = 1 AND EventDate > NOW()) as upcoming_events
";
$statsResult = $conn->query($statsQuery);
$stats = $statsResult ? $statsResult->fetch_assoc() : ['total_events' => 0, 'total_users' => 0, 'total_bookings' => 0, 'upcoming_events' => 0];

// Fetch featured/popular events
$featuredQuery = "SELECT e.*, o.OrganizerName,
                         COALESCE(booking_counts.popularity_score, 0) as popularity_score
                  FROM Events e
                  LEFT JOIN EventOrganizers o ON e.OrganizerId = o.OrganizerId
                  LEFT JOIN (
                      SELECT EventId, COUNT(BookingId) as popularity_score
                      FROM Bookings
                      WHERE PaymentStatus = 'Completed'
                      GROUP BY EventId
                  ) booking_counts ON e.EventId = booking_counts.EventId
                  WHERE e.IsApproved = 1 AND e.EventDate > NOW()
                  ORDER BY popularity_score DESC, e.EventDate ASC
                  LIMIT 6";

$featuredResult = $conn->query($featuredQuery);
if (!$featuredResult) {
    // Fallback to simple query if there's an error
    $featuredQuery = "SELECT * FROM Events WHERE IsApproved = 1 LIMIT 6";
    $featuredResult = $conn->query($featuredQuery);
}

// Get upcoming events
$upcomingQuery = "SELECT e.*, o.OrganizerName
                  FROM Events e
                  LEFT JOIN EventOrganizers o ON e.OrganizerId = o.OrganizerId
                  WHERE e.IsApproved = 1 AND e.EventDate > NOW()
                  ORDER BY e.EventDate ASC
                  LIMIT 8";
$upcomingResult = $conn->query($upcomingQuery);
if (!$upcomingResult) {
    // Fallback to simple query
    $upcomingQuery = "SELECT * FROM Events WHERE IsApproved = 1 LIMIT 8";
    $upcomingResult = $conn->query($upcomingQuery);
}

// Get user's recent bookings if logged in
$userBookings = [];
if ($isLoggedIn && isset($_SESSION['user_id'])) {
    $userBookingsQuery = "SELECT b.*, e.Title as EventTitle, e.EventDate, e.Place
                          FROM Bookings b
                          LEFT JOIN Events e ON b.EventId = e.EventId
                          WHERE b.UserId = ?
                          ORDER BY b.BookingDate DESC
                          LIMIT 3";
    $stmt = $conn->prepare($userBookingsQuery);
    if ($stmt) {
        $stmt->bind_param("s", $_SESSION['user_id']);
        $stmt->execute();
        $userBookingsResult = $stmt->get_result();
        while ($row = $userBookingsResult->fetch_assoc()) {
            $userBookings[] = $row;
        }
    }
}

// Get event categories
$categoriesQuery = "SELECT EventCategory, COUNT(*) as count 
                    FROM Events 
                    WHERE IsApproved = 1 AND EventDate > NOW() 
                    GROUP BY EventCategory 
                    ORDER BY count DESC";
$categoriesResult = $conn->query($categoriesQuery);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Addis Tickets - Premium Event Booking Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a6fd8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--bg-secondary);
            overflow-x: hidden;
        }

        /* Modern Navigation */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            transition: var(--transition);
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 16px;
            font-weight: 800;
            font-size: 24px;
            color: var(--primary-color);
            text-decoration: none;
        }

        .logo img {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            object-fit: cover;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
            transition: var(--transition);
            position: relative;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .nav-links a.active::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-color);
            border-radius: 1px;
        }

        .auth-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-profile {
            position: relative;
        }

        .user-dropdown {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            background: var(--bg-tertiary);
        }

        .user-dropdown:hover {
            background: var(--border-color);
        }

        .user-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--border-color);
            min-width: 220px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition);
            z-index: 1000;
        }

        .user-dropdown:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-menu a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--text-primary);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
        }

        .dropdown-menu a:hover {
            background: var(--bg-secondary);
            color: var(--primary-color);
        }

        .dropdown-divider {
            height: 1px;
            background: var(--border-color);
            margin: 8px 0;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-secondary);
        }

        .cart-icon {
            position: relative;
            padding: 12px;
            border-radius: var(--border-radius);
            background: var(--bg-tertiary);
            cursor: pointer;
            transition: var(--transition);
        }

        .cart-icon:hover {
            background: var(--border-color);
        }

        .cart-count {
            position: absolute;
            top: 4px;
            right: 4px;
            background: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 120px 0 80px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="white" stop-opacity="0.1"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            opacity: 0.1;
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 1;
        }

        .hero-content {
            max-width: 800px;
            text-align: center;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 24px;
            line-height: 1.2;
        }

        .hero p {
            font-size: 20px;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 32px;
            margin-top: 60px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            display: block;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        /* Search Section */
        .search-section {
            background: white;
            margin: -40px auto 0;
            max-width: 800px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-xl);
            padding: 32px;
            position: relative;
            z-index: 10;
        }

        .search-form {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 16px 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 16px;
            transition: var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            padding: 16px 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Main Content */
        .main-content {
            max-width: 1400px;
            margin: 80px auto 0;
            padding: 0 24px;
        }

        /* Dashboard for logged in users */
        .user-dashboard {
            background: white;
            border-radius: var(--border-radius);
            padding: 32px;
            margin-bottom: 40px;
            box-shadow: var(--shadow-md);
        }

        .dashboard-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32px;
        }

        .dashboard-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .dashboard-card {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 24px;
            border: 1px solid var(--border-color);
        }

        .dashboard-card h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        /* Section Headers */
        .section-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .section-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Event Cards */
        .events-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 32px;
            margin-bottom: 80px;
        }

        .event-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }

        .event-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .event-image {
            position: relative;
            height: 220px;
            overflow: hidden;
        }

        .event-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .event-card:hover .event-image img {
            transform: scale(1.05);
        }

        .event-badge {
            position: absolute;
            top: 16px;
            left: 16px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .event-content {
            padding: 24px;
        }

        .event-date {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .event-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 12px;
            color: var(--text-primary);
            line-height: 1.3;
        }

        .event-location {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 16px;
        }

        .event-price {
            font-size: 18px;
            font-weight: 700;
            color: var(--success-color);
            margin-bottom: 20px;
        }

        .event-actions {
            display: flex;
            gap: 12px;
        }

        .btn-select-seats {
            flex: 1;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .btn-select-seats:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Categories */
        .categories-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 40px;
            margin-bottom: 80px;
            box-shadow: var(--shadow-md);
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .category-card {
            text-align: center;
            padding: 24px;
            border-radius: var(--border-radius);
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            cursor: pointer;
        }

        .category-card:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-4px);
        }

        .category-icon {
            font-size: 2.5rem;
            margin-bottom: 16px;
            color: var(--primary-color);
        }

        .category-card:hover .category-icon {
            color: white;
        }

        .category-name {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .category-count {
            font-size: 14px;
            opacity: 0.7;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero {
                padding: 100px 0 60px;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .search-form {
                flex-direction: column;
            }

            .search-input {
                min-width: auto;
            }

            .events-grid {
                grid-template-columns: 1fr;
            }

            .categories-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-light);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="#" class="logo">
                <img src="OIP.jpg" alt="Addis Tickets">
                <span>Addis Tickets</span>
            </a>

            <ul class="nav-links">
                <li><a href="#" class="active">Home</a></li>
                <li><a href="#events">Events</a></li>
                <li><a href="#categories">Categories</a></li>
                <li><a href="support.php">Support</a></li>
            </ul>

            <div class="auth-section">
                <?php if ($isLoggedIn): ?>
                    <div class="user-profile">
                        <div class="user-dropdown">
                            <span class="user-name">
                                <?= htmlspecialchars($_SESSION['first_name'] ?? explode('@', $_SESSION['user_email'] ?? 'User')[0]) ?>
                            </span>
                            <div class="user-avatar">
                                <?= strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)) ?>
                            </div>
                            <div class="dropdown-menu">
                                <a href="profile.php">
                                    <i class="fas fa-user"></i> My Profile
                                </a>
                                <a href="tickets.php">
                                    <i class="fas fa-ticket-alt"></i> My Tickets
                                </a>
                                <a href="support.php">
                                    <i class="fas fa-headset"></i> Support
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="logout.php">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="login.php" class="btn btn-secondary">Login</a>
                    <a href="register.php" class="btn btn-primary">Register</a>
                    <a href="organizer_login.php" class="btn btn-secondary">
                        <i class="fas fa-calendar-alt"></i> Organizer
                    </a>
                <?php endif; ?>

                <div class="cart-icon" id="cartIcon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1>Experience Ethiopia's Premier Events</h1>
                <p>Book tickets for concerts, sports, cultural events, and more at Addis Ababa's most prestigious venues. Join thousands of satisfied customers in unforgettable experiences.</p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['total_events'] ?? 0) ?>+</span>
                        <span class="stat-label">Total Events</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['total_users'] ?? 0) ?>+</span>
                        <span class="stat-label">Happy Customers</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['total_bookings'] ?? 0) ?>+</span>
                        <span class="stat-label">Tickets Sold</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?= number_format($stats['upcoming_events'] ?? 0) ?></span>
                        <span class="stat-label">Upcoming Events</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <div class="main-content">
        <div class="search-section">
            <form class="search-form" action="search_events.php" method="GET">
                <input type="text" name="q" class="search-input" placeholder="Search for events, artists, venues..." />
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i> Search Events
                </button>
            </form>
        </div>

        <?php if ($isLoggedIn && !empty($userBookings)): ?>
        <!-- User Dashboard -->
        <div class="user-dashboard">
            <div class="dashboard-header">
                <h2 class="dashboard-title">Welcome back, <?= htmlspecialchars($_SESSION['first_name'] ?? 'User') ?>!</h2>
                <a href="tickets.php" class="btn btn-primary">View All Tickets</a>
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3><i class="fas fa-ticket-alt"></i> Recent Bookings</h3>
                    <?php foreach (array_slice($userBookings, 0, 3) as $booking): ?>
                        <div style="padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                            <div style="font-weight: 600; margin-bottom: 4px;">
                                <?= htmlspecialchars($booking['EventTitle'] ?? 'Event') ?>
                            </div>
                            <div style="font-size: 14px; color: var(--text-secondary);">
                                <?= $booking['EventDate'] ? date('M d, Y', strtotime($booking['EventDate'])) : 'Date TBA' ?> •
                                <?= htmlspecialchars($booking['Place'] ?? 'Venue TBA') ?>
                            </div>
                            <div style="font-size: 12px; margin-top: 4px;">
                                <span style="padding: 2px 8px; border-radius: 12px; background: <?= $booking['PaymentStatus'] === 'Completed' ? 'var(--success-color)' : 'var(--warning-color)' ?>; color: white;">
                                    <?= ucfirst($booking['PaymentStatus'] ?? 'Pending') ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="dashboard-card">
                    <h3><i class="fas fa-star"></i> Quick Actions</h3>
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <a href="tickets.php" class="btn btn-secondary" style="justify-content: flex-start;">
                            <i class="fas fa-ticket-alt"></i> View My Tickets
                        </a>
                        <a href="profile.php" class="btn btn-secondary" style="justify-content: flex-start;">
                            <i class="fas fa-user"></i> Edit Profile
                        </a>
                        <a href="support.php" class="btn btn-secondary" style="justify-content: flex-start;">
                            <i class="fas fa-headset"></i> Get Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Featured Events -->
        <?php if ($featuredResult->num_rows > 0): ?>
        <section id="featured">
            <div class="section-header">
                <h2 class="section-title">Featured Events</h2>
                <p class="section-subtitle">Don't miss these popular and trending events</p>
            </div>

            <div class="events-grid">
                <?php while ($event = $featuredResult->fetch_assoc()): ?>
                    <div class="event-card">
                        <div class="event-image">
                            <?php if (!empty($event['EventImage'])): ?>
                                <img src="<?= htmlspecialchars($event['EventImage']) ?>" alt="<?= htmlspecialchars($event['Title']) ?>">
                            <?php else: ?>
                                <img src="https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?w=400&h=300&fit=crop" alt="Event">
                            <?php endif; ?>
                            <div class="event-badge"><?= htmlspecialchars($event['EventCategory'] ?? 'Event') ?></div>
                        </div>
                        <div class="event-content">
                            <div class="event-date">
                                <?= $event['EventDate'] ? date('D, M j • g:i A', strtotime($event['EventDate'])) : 'Date TBA' ?>
                            </div>
                            <h3 class="event-title"><?= htmlspecialchars($event['Title']) ?></h3>
                            <div class="event-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <?= htmlspecialchars($event['Place'] ?? 'Venue TBA') ?>
                            </div>
                            <div class="event-price">
                                <?php if ($event['EventCategory'] === 'Cultural' && strpos(strtolower($event['Title']), 'meskel') !== false): ?>
                                    FREE ENTRY
                                <?php else: ?>
                                    
                                <?php endif; ?>
                            </div>
                            <div class="event-actions">
                                <button class="btn-select-seats" onclick="selectSeats('<?= $event['EventId'] ?>')">
                                    <i class="fas fa-chair"></i> Select Seats
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- All Events -->
        <?php if ($upcomingResult->num_rows > 0): ?>
        <section id="events">
            <div class="section-header">
                <h2 class="section-title">Upcoming Events</h2>
                <p class="section-subtitle">Discover all the exciting events happening soon</p>
            </div>

            <div class="events-grid">
                <?php while ($event = $upcomingResult->fetch_assoc()): ?>
                    <div class="event-card">
                        <div class="event-image">
                            <?php if (!empty($event['EventImage'])): ?>
                                <img src="<?= htmlspecialchars($event['EventImage']) ?>" alt="<?= htmlspecialchars($event['Title']) ?>">
                            <?php else: ?>
                                <img src="https://images.unsplash.com/photo-1540039155733-5bb30b53aa14?w=400&h=300&fit=crop" alt="Event">
                            <?php endif; ?>
                            <div class="event-badge"><?= htmlspecialchars($event['EventCategory'] ?? 'Event') ?></div>
                        </div>
                        <div class="event-content">
                            <div class="event-date">
                                <?= $event['EventDate'] ? date('D, M j • g:i A', strtotime($event['EventDate'])) : 'Date TBA' ?>
                            </div>
                            <h3 class="event-title"><?= htmlspecialchars($event['Title']) ?></h3>
                            <div class="event-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <?= htmlspecialchars($event['Place'] ?? 'Venue TBA') ?>
                            </div>
                            <div class="event-price">
                                <?php if ($event['EventCategory'] === 'Cultural' && strpos(strtolower($event['Title']), 'meskel') !== false): ?>
                                    FREE ENTRY
                                <?php else: ?>
                                    
                                <?php endif; ?>
                            </div>
                            <div class="event-actions">
                                <button class="btn-select-seats" onclick="selectSeats('<?= $event['EventId'] ?>')">
                                    <i class="fas fa-chair"></i> Select Seats
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- Categories -->
        <?php if ($categoriesResult->num_rows > 0): ?>
        <section id="categories" class="categories-section">
            <div class="section-header">
                <h2 class="section-title">Event Categories</h2>
                <p class="section-subtitle">Browse events by category to find what interests you most</p>
            </div>

            <div class="categories-grid">
                <?php while ($category = $categoriesResult->fetch_assoc()): ?>
                    <div class="category-card" onclick="searchByCategory('<?= htmlspecialchars($category['EventCategory']) ?>')">
                        <div class="category-icon">
                            <?php
                            $icons = [
                                'Sports' => 'fas fa-futbol',
                                'Concert' => 'fas fa-music',
                                'Cultural' => 'fas fa-theater-masks',
                                'Conference' => 'fas fa-users',
                                'Other' => 'fas fa-calendar-alt'
                            ];
                            $icon = $icons[$category['EventCategory']] ?? 'fas fa-calendar-alt';
                            ?>
                            <i class="<?= $icon ?>"></i>
                        </div>
                        <div class="category-name"><?= htmlspecialchars($category['EventCategory']) ?></div>
                        <div class="category-count"><?= $category['count'] ?> events</div>
                    </div>
                <?php endwhile; ?>
            </div>
        </section>
        <?php endif; ?>

        <!-- Features Section -->
        <section class="categories-section">
            <div class="section-header">
                <h2 class="section-title">Why Choose Addis Tickets?</h2>
                <p class="section-subtitle">We provide the best ticket booking experience in Ethiopia</p>
            </div>

            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="category-name">Secure Booking</div>
                    <div class="category-count">100% secure transactions</div>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="category-name">Mobile Tickets</div>
                    <div class="category-count">Instant e-tickets</div>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="category-name">24/7 Support</div>
                    <div class="category-count">Always here to help</div>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="category-name">Local Payments</div>
                    <div class="category-count">Telebirr, CBE Birr & more</div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer style="background: var(--text-primary); color: white; padding: 60px 0 30px; margin-top: 80px;">
        <div style="max-width: 1400px; margin: 0 auto; padding: 0 24px;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px;">
                <div>
                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 20px;">
                        <img src="OIP.jpg" alt="Addis Tickets" style="width: 40px; height: 40px; border-radius: 8px;">
                        <span style="font-size: 20px; font-weight: 700;">Addis Tickets</span>
                    </div>
                    <p style="color: #9ca3af; line-height: 1.6; margin-bottom: 20px;">
                        Ethiopia's premier event booking platform. Experience the best concerts, sports, and cultural events.
                    </p>
                    <div style="display: flex; gap: 16px;">
                        <a href="#" style="color: #9ca3af; font-size: 20px; transition: var(--transition);"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" style="color: #9ca3af; font-size: 20px; transition: var(--transition);"><i class="fab fa-twitter"></i></a>
                        <a href="#" style="color: #9ca3af; font-size: 20px; transition: var(--transition);"><i class="fab fa-instagram"></i></a>
                        <a href="#" style="color: #9ca3af; font-size: 20px; transition: var(--transition);"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
                <div>
                    <h3 style="margin-bottom: 20px; font-weight: 600;">Quick Links</h3>
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <a href="#" style="color: #9ca3af; text-decoration: none; transition: var(--transition);">Home</a>
                        <a href="#events" style="color: #9ca3af; text-decoration: none; transition: var(--transition);">Events</a>
                        <a href="tickets.php" style="color: #9ca3af; text-decoration: none; transition: var(--transition);">My Tickets</a>
                        <a href="support.php" style="color: #9ca3af; text-decoration: none; transition: var(--transition);">Support</a>
                    </div>
                </div>
                <div>
                    <h3 style="margin-bottom: 20px; font-weight: 600;">Contact Info</h3>
                    <div style="display: flex; flex-direction: column; gap: 12px; color: #9ca3af;">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Addis Ababa Stadium, Ethiopia</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <i class="fas fa-phone"></i>
                            <span>+251 945653317</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                    </div>
                </div>
            </div>
            <div style="border-top: 1px solid #374151; padding-top: 30px; text-align: center; color: #9ca3af;">
                <p>&copy; <?= date('Y') ?> Addis Tickets. All rights reserved. | Built with ❤️ in Ethiopia</p>
            </div>
        </div>
    </footer>

    <script>
        // Modern JavaScript for enhanced user experience

        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        function updateCartDisplay() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + (item.quantity || 1), 0);
            cartCount.textContent = totalItems;
            cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
        }

        // Select seats function
        function selectSeats(eventId) {
            <?php if ($isLoggedIn): ?>
                // Add loading state
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="loading"></span> Loading...';
                button.disabled = true;

                // Simulate loading then redirect
                setTimeout(() => {
                    window.location.href = 'working_seat_selection.php?event_id=' + eventId;
                }, 500);
            <?php else: ?>
                // Show modern confirmation dialog
                if (confirm('🎫 You need to login to select seats.\n\nWould you like to go to the login page?')) {
                    window.location.href = 'login.php?redirect=' + encodeURIComponent('working_seat_selection.php?event_id=' + eventId);
                }
            <?php endif; ?>
        }

        // Search by category
        function searchByCategory(category) {
            window.location.href = 'search_events.php?category=' + encodeURIComponent(category);
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced search functionality
        const searchForm = document.querySelector('.search-form');
        const searchInput = document.querySelector('.search-input');

        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                const query = searchInput.value.trim();
                if (!query) {
                    e.preventDefault();
                    searchInput.focus();
                    searchInput.style.borderColor = 'var(--error-color)';
                    setTimeout(() => {
                        searchInput.style.borderColor = '';
                    }, 2000);
                }
            });
        }

        // Cart icon click handler
        document.getElementById('cartIcon').addEventListener('click', function() {
            if (cart.length === 0) {
                showNotification('Your cart is empty! Add some tickets first.', 'info');
                return;
            }

            <?php if ($isLoggedIn): ?>
                window.location.href = 'working_checkout.php';
            <?php else: ?>
                if (confirm('🛒 You have items in your cart!\n\nLogin to proceed to checkout?')) {
                    window.location.href = 'login.php?redirect=working_checkout.php';
                }
            <?php endif; ?>
        });

        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-color)' : 'var(--primary-color)'};
                color: white;
                padding: 16px 24px;
                border-radius: var(--border-radius);
                box-shadow: var(--shadow-xl);
                z-index: 10000;
                font-weight: 600;
                transform: translateX(400px);
                transition: var(--transition);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Initialize cart display
        updateCartDisplay();

        // Console welcome message
        console.log('%c🎫 Welcome to Addis Tickets!', 'color: #667eea; font-size: 20px; font-weight: bold;');
        console.log('%cBuilt with ❤️ in Ethiopia', 'color: #10b981; font-size: 14px;');
    </script>
</body>
</html>
<?php $conn->close(); ?>
