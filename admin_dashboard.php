<?php
session_start();
require_once 'db_connect.php';

// Check if user is admin - SECURE VERSION
if (!isset($_SESSION['admin_loggedin']) || $_SESSION['admin_loggedin'] !== true) {
    // Redirect to admin login if not authenticated
    header("Location: admin_login.php");
    exit();
}

// Get quick stats
try {
    $stats = [];
    
    // Total users
    $userStmt = $pdo->query("SELECT COUNT(*) as count FROM Users");
    $stats['users'] = $userStmt->fetch()['count'] ?? 0;
    
    // Total events
    $eventStmt = $pdo->query("SELECT COUNT(*) as count FROM Events WHERE IsApproved = 1");
    $stats['events'] = $eventStmt->fetch()['count'] ?? 0;
    
    // Pending events
    $pendingStmt = $pdo->query("SELECT COUNT(*) as count FROM Events WHERE IsApproved = 0");
    $stats['pending'] = $pendingStmt->fetch()['count'] ?? 0;
    
    // Total bookings
    $bookingStmt = $pdo->query("SELECT COUNT(*) as count FROM Bookings");
    $stats['bookings'] = $bookingStmt->fetch()['count'] ?? 0;
    
    // Total revenue
    $revenueStmt = $pdo->query("SELECT COALESCE(SUM(Amount), 0) as total FROM Payments");
    $stats['revenue'] = $revenueStmt->fetch()['total'] ?? 0;

    // Feedback statistics
    $feedbackStmt = $pdo->query("
        SELECT
            COUNT(*) as total_feedback,
            COUNT(CASE WHEN Status = 'Open' THEN 1 END) as open_feedback,
            COUNT(CASE WHEN Status = 'In Progress' THEN 1 END) as in_progress_feedback
        FROM SupportTickets
        WHERE Category IN ('feedback', 'general', 'booking', 'technical', 'payment')
    ");
    $feedbackStats = $feedbackStmt->fetch();
    $stats['total_feedback'] = $feedbackStats['total_feedback'] ?? 0;
    $stats['open_feedback'] = $feedbackStats['open_feedback'] ?? 0;
    $stats['in_progress_feedback'] = $feedbackStats['in_progress_feedback'] ?? 0;

    // Recent bookings
    $recentStmt = $pdo->prepare("
        SELECT b.BookingId, b.BookingDate, b.TotalAmount, e.Title, u.FirstName, u.LastName
        FROM Bookings b
        LEFT JOIN Events e ON b.EventId = e.EventId
        LEFT JOIN Users u ON b.UserId = u.UserId
        ORDER BY b.BookingDate DESC
        LIMIT 5
    ");
    $recentStmt->execute();
    $recentBookings = $recentStmt->fetchAll();

    // Recent feedback
    $recentFeedbackStmt = $pdo->prepare("
        SELECT st.TicketId, st.Subject, st.Message, st.Status, st.CreatedDate, st.Category,
               CASE WHEN st.Message LIKE 'Name: %' THEN
                   SUBSTRING_INDEX(SUBSTRING_INDEX(st.Message, 'Name: ', -1), CHAR(10), 1)
                   ELSE 'Anonymous' END as CustomerName
        FROM SupportTickets st
        WHERE st.Category IN ('feedback', 'general', 'booking', 'technical', 'payment')
        ORDER BY st.CreatedDate DESC
        LIMIT 5
    ");
    $recentFeedbackStmt->execute();
    $recentFeedback = $recentFeedbackStmt->fetchAll();

} catch (Exception $e) {
    $stats = [
        'users' => 0,
        'events' => 0,
        'pending' => 0,
        'bookings' => 0,
        'revenue' => 0,
        'total_feedback' => 0,
        'open_feedback' => 0,
        'in_progress_feedback' => 0
    ];
    $recentBookings = [];
    $recentFeedback = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .header-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .header-actions a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
            transition: background 0.3s;
            font-size: 14px;
        }
        .header-actions a:hover {
            background: rgba(255,255,255,0.3);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card .icon {
            font-size: 40px;
            margin-bottom: 15px;
        }
        .stat-card .value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-card .label {
            color: #666;
            font-size: 14px;
        }
        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .admin-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .admin-card:hover {
            transform: translateY(-3px);
        }
        .admin-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .admin-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
            font-size: 14px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-warning:hover { background: #e0a800; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .recent-activity {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .recent-activity h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-info {
            flex: 1;
        }
        .activity-info .title {
            font-weight: 600;
            color: #333;
        }
        .activity-info .details {
            font-size: 14px;
            color: #666;
        }
        .activity-amount {
            font-weight: bold;
            color: #28a745;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-open {
            background: #fff3cd;
            color: #856404;
        }
        .status-in-progress {
            background: #cce5ff;
            color: #004085;
        }
        .status-resolved {
            background: #d4edda;
            color: #155724;
        }
        .status-closed {
            background: #f8d7da;
            color: #721c24;
        }
        @media (max-width: 768px) {
            .header-actions {
                flex-direction: column;
            }
            .admin-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
        <div class="header-actions">
            <a href="Finalll_updated.php"><i class="fas fa-home"></i> Main Site</a>
            <a href="admin_analytics.php"><i class="fas fa-chart-line"></i> Analytics</a>
            <a href="admin_feedback.php"><i class="fas fa-comments"></i> Feedback</a>
            <a href="admin_settings.php"><i class="fas fa-cog"></i> Settings</a>
            <a href="admin.php?tab=users"><i class="fas fa-users"></i> Users</a>
            <a href="admin.php?tab=events"><i class="fas fa-calendar"></i> Events</a>
            <a href="admin.php?tab=organizers"><i class="fas fa-building"></i> Organizers</a>
            <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>

    <div class="container">
        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon" style="color: #667eea;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="value"><?= number_format($stats['users']) ?></div>
                <div class="label">Total Users</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #28a745;">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="value"><?= number_format($stats['events']) ?></div>
                <div class="label">Active Events</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #ffc107;">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="value"><?= number_format($stats['pending']) ?></div>
                <div class="label">Pending Approval</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #17a2b8;">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="value"><?= number_format($stats['bookings']) ?></div>
                <div class="label">Total Bookings</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #dc3545;">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="value"><?= number_format($stats['revenue'], 2) ?> ETB</div>
                <div class="label">Total Revenue</div>
            </div>

            <div class="stat-card">
                <div class="icon" style="color: #6f42c1;">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="value"><?= number_format($stats['total_feedback']) ?></div>
                <div class="label">Total Feedback</div>
            </div>
        </div>

        <!-- Admin Functions -->
        <div class="admin-grid">
            <div class="admin-card">
                <h3><i class="fas fa-chart-line"></i> Analytics & Reports</h3>
                <p>View detailed analytics, revenue reports, and performance metrics for your platform.</p>
                <a href="admin_analytics.php" class="btn">
                    <i class="fas fa-chart-bar"></i> View Analytics
                </a>
            </div>

            <div class="admin-card">
                <h3><i class="fas fa-cog"></i> System Settings</h3>
                <p>Configure payment methods, refund policies, email settings, and manage FAQs.</p>
                <a href="admin_settings.php" class="btn">
                    <i class="fas fa-wrench"></i> Manage Settings
                </a>
            </div>

            <div class="admin-card">
                <h3><i class="fas fa-calendar-check"></i> Event Management</h3>
                <p>Approve pending events, manage event details, and oversee event organizers.</p>
                <a href="admin.php?tab=events" class="btn btn-success">
                    <i class="fas fa-calendar-plus"></i> Manage Events
                </a>
            </div>

            <div class="admin-card">
                <h3><i class="fas fa-users-cog"></i> User Management</h3>
                <p>View user accounts, manage permissions, and handle user-related issues.</p>
                <a href="admin.php?tab=users" class="btn btn-warning">
                    <i class="fas fa-user-edit"></i> Manage Users
                </a>
            </div>

            <div class="admin-card">
                <h3><i class="fas fa-ticket-alt"></i> Booking Management</h3>
                <p>View all bookings, process refunds, and handle booking-related support.</p>
                <a href="admin.php?tab=tickets" class="btn">
                    <i class="fas fa-list"></i> View Bookings
                </a>
            </div>

            <div class="admin-card">
                <h3><i class="fas fa-building"></i> Organizer Management</h3>
                <p>Add new event organizers, approve organizer accounts, and manage organizer permissions and events.</p>
                <a href="admin.php?tab=organizers" class="btn btn-info">
                    <i class="fas fa-user-plus"></i> Manage Organizers
                </a>
            </div>

            <div class="admin-card">
                <h3><i class="fas fa-comments"></i> Feedback Management</h3>
                <p>View and manage user feedback, ratings, and suggestions to improve the platform.</p>
                <div style="margin-bottom: 15px;">
                    <small style="color: #666;">
                        <i class="fas fa-exclamation-circle" style="color: #ffc107;"></i>
                        <?= $stats['open_feedback'] ?> open feedback items
                        <?php if ($stats['in_progress_feedback'] > 0): ?>
                            • <?= $stats['in_progress_feedback'] ?> in progress
                        <?php endif; ?>
                    </small>
                </div>
                <a href="admin_feedback.php" class="btn" style="background: #6f42c1;">
                    <i class="fas fa-comments"></i> Manage Feedback
                </a>
            </div>

            <div class="admin-card">
                <h3><i class="fas fa-headset"></i> Support Center</h3>
                <p>Manage customer support tickets, FAQs, and communication with users.</p>
                <a href="support.php" class="btn">
                    <i class="fas fa-life-ring"></i> Support Center
                </a>
            </div>
        </div>

        <!-- Recent Activity Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 30px;">
            <!-- Recent Bookings -->
            <div class="recent-activity">
                <h3><i class="fas fa-clock"></i> Recent Bookings</h3>
                <?php if (!empty($recentBookings)): ?>
                    <?php foreach ($recentBookings as $booking): ?>
                        <div class="activity-item">
                            <div class="activity-info">
                                <div class="title">
                                    <?= htmlspecialchars($booking['FirstName'] ?? 'Unknown') ?>
                                    <?= htmlspecialchars($booking['LastName'] ?? 'User') ?>
                                </div>
                                <div class="details">
                                    <?= htmlspecialchars($booking['Title'] ?? 'Event') ?> •
                                    <?= date('M d, Y H:i', strtotime($booking['BookingDate'])) ?>
                                </div>
                            </div>
                            <div class="activity-amount">
                                <?= number_format($booking['TotalAmount'], 2) ?> ETB
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="activity-item">
                        <div class="activity-info">
                            <div class="details">No recent bookings found.</div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Feedback -->
            <div class="recent-activity">
                <h3><i class="fas fa-comments"></i> Recent Feedback</h3>
                <?php if (!empty($recentFeedback)): ?>
                    <?php foreach ($recentFeedback as $feedback): ?>
                        <div class="activity-item">
                            <div class="activity-info">
                                <div class="title">
                                    <?= htmlspecialchars($feedback['CustomerName']) ?>
                                </div>
                                <div class="details">
                                    <?= htmlspecialchars(substr($feedback['Subject'], 0, 50)) ?><?= strlen($feedback['Subject']) > 50 ? '...' : '' ?> •
                                    <?= date('M d, Y H:i', strtotime($feedback['CreatedDate'])) ?>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="status-badge status-<?= strtolower(str_replace(' ', '-', $feedback['Status'])) ?>" style="font-size: 10px; padding: 2px 8px;">
                                    <?= $feedback['Status'] ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="admin_feedback.php" class="btn" style="background: #6f42c1; font-size: 12px; padding: 8px 16px;">
                            <i class="fas fa-comments"></i> View All Feedback
                        </a>
                    </div>
                <?php else: ?>
                    <div class="activity-item">
                        <div class="activity-info">
                            <div class="details">No recent feedback found.</div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
