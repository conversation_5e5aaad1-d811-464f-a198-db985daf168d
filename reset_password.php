<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

$token = $_GET['token'] ?? '';
$message = '';
$error = '';
$validToken = false;
$user = null;

// Validate token
if (empty($token)) {
    $error = "Invalid reset link.";
} else {
    try {
        // Check if token exists and is not expired
        $stmt = $pdo->prepare("
            SELECT UserId, FirstName, Email 
            FROM Users 
            WHERE PasswordResetToken = ? 
            AND PasswordResetExpiry > NOW()
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if ($user) {
            $validToken = true;
        } else {
            $error = "Invalid or expired reset link. Please request a new password reset.";
        }
        
    } catch (Exception $e) {
        $error = "An error occurred. Please try again.";
        logSecurityEvent('password_reset_validation_error', ['error' => $e->getMessage()]);
    }
}

// Handle password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $validToken) {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate passwords
        if (empty($password) || empty($confirmPassword)) {
            $error = "Please fill in all fields.";
        } elseif ($password !== $confirmPassword) {
            $error = "Passwords do not match.";
        } else {
            // Validate password strength
            $passwordErrors = validatePassword($password);
            if (!empty($passwordErrors)) {
                $error = "Password requirements not met: " . implode(', ', $passwordErrors);
            } else {
                try {
                    // Update password and clear reset token
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    $updateStmt = $pdo->prepare("
                        UPDATE Users 
                        SET PasswordHash = ?, PasswordResetToken = NULL, PasswordResetExpiry = NULL 
                        WHERE UserId = ?
                    ");
                    $updateStmt->execute([$passwordHash, $user['UserId']]);
                    
                    $message = "Password reset successfully! You can now log in with your new password.";
                    
                    // Log the successful reset
                    logSecurityEvent('password_reset_completed', [
                        'user_id' => $user['UserId'],
                        'email' => $user['Email']
                    ]);
                    
                    $validToken = false; // Prevent further resets
                    
                } catch (Exception $e) {
                    $error = "Failed to reset password. Please try again.";
                    logSecurityEvent('password_reset_update_error', ['error' => $e->getMessage()]);
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .reset-container {
            max-width: 500px;
            margin: 100px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .reset-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .reset-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .password-requirements {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .password-requirements ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .password-strength {
            margin-top: 5px;
            height: 5px;
            background: #eee;
            border-radius: 3px;
            overflow: hidden;
        }
        .password-strength-bar {
            height: 100%;
            transition: all 0.3s ease;
        }
        .strength-weak { background: #dc3545; width: 25%; }
        .strength-fair { background: #ffc107; width: 50%; }
        .strength-good { background: #28a745; width: 75%; }
        .strength-strong { background: #007bff; width: 100%; }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <img alt="Addis Ababa Stadium Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
        </nav>
    </header>

    <div class="reset-container">
        <div class="reset-header">
            <i class="fas fa-lock reset-icon"></i>
            <h2>Reset Password</h2>
            <?php if ($validToken && $user): ?>
                <p>Create a new password for <?= htmlspecialchars($user['Email']) ?></p>
            <?php endif; ?>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                <div style="margin-top: 15px;">
                    <a href="login.php" class="btn">Go to Login</a>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if ($validToken && !$message): ?>
        <form method="POST" id="resetForm">
            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
            
            <div class="password-requirements">
                <h4><i class="fas fa-shield-alt"></i> Password Requirements:</h4>
                <ul>
                    <li>At least 8 characters long</li>
                    <li>Contains uppercase and lowercase letters</li>
                    <li>Contains at least one number</li>
                    <li>Contains at least one special character</li>
                </ul>
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> New Password
                </label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    placeholder="Enter your new password"
                    required
                    minlength="8"
                >
                <div class="password-strength">
                    <div class="password-strength-bar" id="strengthBar"></div>
                </div>
                <small id="strengthText"></small>
            </div>

            <div class="form-group">
                <label for="confirm_password">
                    <i class="fas fa-lock"></i> Confirm New Password
                </label>
                <input 
                    type="password" 
                    id="confirm_password" 
                    name="confirm_password" 
                    placeholder="Confirm your new password"
                    required
                    minlength="8"
                >
                <small id="matchText"></small>
            </div>

            <button type="submit" class="btn" id="submitBtn">
                <i class="fas fa-save"></i> Reset Password
            </button>
        </form>
        <?php endif; ?>

        <div class="back-link">
            <a href="login.php">
                <i class="fas fa-arrow-left"></i> Back to Login
            </a>
            |
            <a href="forgot_password.php">
                <i class="fas fa-key"></i> Request New Reset Link
            </a>
        </div>
    </div>

    <script>
        const passwordInput = document.getElementById('password');
        const confirmInput = document.getElementById('confirm_password');
        const strengthBar = document.getElementById('strengthBar');
        const strengthText = document.getElementById('strengthText');
        const matchText = document.getElementById('matchText');
        const submitBtn = document.getElementById('submitBtn');

        function checkPasswordStrength(password) {
            let strength = 0;
            let feedback = [];

            if (password.length >= 8) strength++;
            else feedback.push('at least 8 characters');

            if (/[a-z]/.test(password)) strength++;
            else feedback.push('lowercase letter');

            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('uppercase letter');

            if (/[0-9]/.test(password)) strength++;
            else feedback.push('number');

            if (/[^A-Za-z0-9]/.test(password)) strength++;
            else feedback.push('special character');

            return { strength, feedback };
        }

        passwordInput?.addEventListener('input', function() {
            const password = this.value;
            const result = checkPasswordStrength(password);
            
            strengthBar.className = 'password-strength-bar';
            
            if (result.strength <= 1) {
                strengthBar.classList.add('strength-weak');
                strengthText.textContent = 'Weak password';
                strengthText.style.color = '#dc3545';
            } else if (result.strength <= 2) {
                strengthBar.classList.add('strength-fair');
                strengthText.textContent = 'Fair password';
                strengthText.style.color = '#ffc107';
            } else if (result.strength <= 3) {
                strengthBar.classList.add('strength-good');
                strengthText.textContent = 'Good password';
                strengthText.style.color = '#28a745';
            } else {
                strengthBar.classList.add('strength-strong');
                strengthText.textContent = 'Strong password';
                strengthText.style.color = '#007bff';
            }

            if (result.feedback.length > 0) {
                strengthText.textContent += ' (Missing: ' + result.feedback.join(', ') + ')';
            }
        });

        confirmInput?.addEventListener('input', function() {
            const password = passwordInput.value;
            const confirm = this.value;
            
            if (confirm === '') {
                matchText.textContent = '';
            } else if (password === confirm) {
                matchText.textContent = '✓ Passwords match';
                matchText.style.color = '#28a745';
            } else {
                matchText.textContent = '✗ Passwords do not match';
                matchText.style.color = '#dc3545';
            }
        });

        document.getElementById('resetForm')?.addEventListener('submit', function(e) {
            const password = passwordInput.value;
            const confirm = confirmInput.value;
            
            if (password !== confirm) {
                e.preventDefault();
                alert('Passwords do not match!');
                return false;
            }
            
            const result = checkPasswordStrength(password);
            if (result.strength < 3) {
                e.preventDefault();
                alert('Password is too weak. Please choose a stronger password.');
                return false;
            }
        });
    </script>
</body>
</html>
