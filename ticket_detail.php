<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) || !$_SESSION['loggedin']) {
    header("Location: login.php");
    exit();
}

// Get booking ID from URL
$bookingId = $_GET['booking_id'] ?? '';

if (empty($bookingId)) {
    header("Location: tickets.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Get ticket details
try {
    $stmt = $pdo->prepare("
        SELECT b.BookingId, b.BookingDate, b.TotalAmount, b.PaymentStatus, b.BookingReference,
               e.EventId, e.Title as EventTitle, e.EventDate, e.EndDate, e.Place, e.Address, e.EventImage, e.Description,
               p.PaymentId, p.PaymentMethod, p.PaymentDate, p.Amount as PaymentAmount,
               u.FirstName, u.LastName, u.Email, u.PhoneNumber
        FROM Bookings b
        LEFT JOIN Events e ON b.EventId = e.EventId
        LEFT JOIN Payments p ON b.BookingId = p.BookingId
        LEFT JOIN Users u ON b.UserId = u.UserId
        WHERE b.BookingId = ? AND b.UserId = ?
    ");
    $stmt->execute([$bookingId, $userId]);
    $ticket = $stmt->fetch();
    
    if (!$ticket) {
        header("Location: tickets.php");
        exit();
    }
} catch (Exception $e) {
    header("Location: tickets.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Details - <?= htmlspecialchars($ticket['EventTitle']) ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
        .logo-text h1 {
            color: white;
            font-size: 1.5em;
            margin-bottom: 2px;
        }
        .logo-text p {
            color: rgba(255,255,255,0.8);
            font-size: 0.9em;
        }
        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .nav-links a:hover {
            color: #ffd700;
        }
        .ticket-container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }
        .ticket-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        .ticket-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .ticket-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 20px solid #764ba2;
        }
        .ticket-id {
            font-size: 1.1em;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        .event-title {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .status-badge {
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
            background: rgba(255,255,255,0.2);
        }
        .ticket-body {
            padding: 40px;
        }
        .qr-section {
            text-align: center;
            margin-bottom: 40px;
        }
        .qr-code {
            width: 150px;
            height: 150px;
            background: #f8f9fa;
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }
        .qr-code i {
            font-size: 4em;
            color: #ccc;
        }
        .qr-text {
            color: #666;
            font-size: 0.9em;
        }
        .ticket-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .detail-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        .detail-label {
            color: #666;
            font-weight: 500;
        }
        .detail-value {
            color: #333;
            font-weight: 600;
        }
        .ticket-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-size: 1em;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }
        .btn-secondary:hover {
            background: #e9ecef;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #218838;
        }
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: color 0.3s ease;
        }
        .back-link:hover {
            color: #5a6fd8;
        }
        @media (max-width: 768px) {
            .ticket-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .ticket-actions {
                flex-direction: column;
            }
            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <img alt="Addis Tickets Logo" src="OIP.jpg">
                <div class="logo-text">
                    <h1>Addis Tickets</h1>
                    <p>Official Booking Partner</p>
                </div>
            </div>
            <ul class="nav-links">
                <li><a href="Finalll_updated.php">Home</a></li>
                <li><a href="profile.php">Profile</a></li>
                <li><a href="tickets.php">My Tickets</a></li>
                <li><a href="support.php">Support</a></li>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </div>
    </nav>

    <div class="ticket-container">
        <a href="tickets.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to My Tickets
        </a>

        <div class="ticket-card">
            <div class="ticket-header">
                <div class="ticket-id">Ticket #<?= htmlspecialchars($ticket['BookingId']) ?></div>
                <div class="event-title"><?= htmlspecialchars($ticket['EventTitle']) ?></div>
                <span class="status-badge">
                    <?= ucfirst($ticket['PaymentStatus'] ?? 'Pending') ?>
                </span>
            </div>
            
            <div class="ticket-body">
                <div class="qr-section">
                    <div class="qr-code">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="qr-text">Scan this QR code at the venue for entry</div>
                </div>
                
                <div class="ticket-details">
                    <div class="detail-section">
                        <h3><i class="fas fa-calendar"></i> Event Details</h3>
                        <div class="detail-item">
                            <span class="detail-label">Date & Time</span>
                            <span class="detail-value">
                                <?= $ticket['EventDate'] ? date('M d, Y H:i', strtotime($ticket['EventDate'])) : 'TBA' ?>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Venue</span>
                            <span class="detail-value"><?= htmlspecialchars($ticket['Place'] ?? 'TBA') ?></span>
                        </div>
                        <?php if ($ticket['Address']): ?>
                        <div class="detail-item">
                            <span class="detail-label">Address</span>
                            <span class="detail-value"><?= htmlspecialchars($ticket['Address']) ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="detail-section">
                        <h3><i class="fas fa-receipt"></i> Booking Details</h3>
                        <div class="detail-item">
                            <span class="detail-label">Booking Date</span>
                            <span class="detail-value"><?= date('M d, Y H:i', strtotime($ticket['BookingDate'])) ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Total Amount</span>
                            <span class="detail-value"><?= number_format($ticket['TotalAmount'], 2) ?> ETB</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Payment Method</span>
                            <span class="detail-value"><?= htmlspecialchars($ticket['PaymentMethod'] ?? 'N/A') ?></span>
                        </div>
                        <?php if ($ticket['PaymentDate']): ?>
                        <div class="detail-item">
                            <span class="detail-label">Payment Date</span>
                            <span class="detail-value"><?= date('M d, Y H:i', strtotime($ticket['PaymentDate'])) ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="ticket-actions">
                    <?php if ($ticket['PaymentStatus'] === 'Completed'): ?>
                        <a href="#" class="btn btn-primary" onclick="downloadTicket('<?= $ticket['BookingId'] ?>')">
                            <i class="fas fa-download"></i> Download PDF
                        </a>
                        <a href="#" class="btn btn-secondary" onclick="printTicket()">
                            <i class="fas fa-print"></i> Print Ticket
                        </a>
                        <a href="#" class="btn btn-secondary" onclick="shareTicket('<?= $ticket['BookingId'] ?>')">
                            <i class="fas fa-share"></i> Share
                        </a>
                    <?php elseif ($ticket['PaymentStatus'] === 'Pending'): ?>
                        <a href="working_checkout.php?booking_id=<?= $ticket['BookingId'] ?>" class="btn btn-success">
                            <i class="fas fa-credit-card"></i> Complete Payment
                        </a>
                    <?php endif; ?>
                    <a href="support.php" class="btn btn-secondary">
                        <i class="fas fa-headset"></i> Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadTicket(bookingId) {
            alert('PDF download functionality will be implemented. Booking ID: ' + bookingId);
            // In a real implementation, this would generate and download a PDF ticket
        }
        
        function printTicket() {
            window.print();
        }
        
        function shareTicket(bookingId) {
            if (navigator.share) {
                navigator.share({
                    title: 'My Event Ticket - <?= htmlspecialchars($ticket['EventTitle']) ?>',
                    text: 'Check out my ticket for this event!',
                    url: window.location.href
                });
            } else {
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Ticket link copied to clipboard!');
                });
            }
        }
    </script>
</body>
</html>
