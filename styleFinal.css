:root {
    --primary: #f15a22; /* Ethiopian orange */
    --secondary: #078930; /* Ethiopian green */
    --light: #f8f9fa;
    --dark: #212529;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
}

/* Header Styles */
header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.top-bar {
    background-color: var(--primary);
    color: white;
    padding: 8px 0;
    text-align: center;
    font-size: 14px;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 5%;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 10px;
}

.logo-text h1 {
    color: var(--primary);
    font-size: 24px;
}

.logo-text p {
    color: var(--secondary);
    font-size: 12px;
    margin-top: -5px;
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 30px;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark);
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: var(--primary);
}

/* Auth Buttons */
.auth-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.login-btn, .register-btn {
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
    cursor: pointer;
}

.login-btn {
    background-color: #28a745;
    color: #ffffff;
    border: none;
}

.register-btn {
    background-color: #6c757d;
    color: #ffffff;
    border: none;
}

.login-btn:hover {
    background-color: #218838;
    box-shadow: 0 0 15px rgba(40, 167, 69, 0.4);
}

.register-btn:hover {
    background-color: #5a6268;
    box-shadow: 0 0 15px rgba(108, 117, 125, 0.4);
}

/* User Dropdown Styles */
.user-dropdown {
    position: relative;
    margin-left: 15px;
}

.user-trigger {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 20px;
    transition: background 0.3s;
}

.user-trigger:hover {
    background: rgba(255,255,255,0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
}

.avatar-placeholder {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4CAF50;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-weight: bold;
}

.user-name {
    margin-right: 8px;
    font-weight: 500;
    color: var(--dark);
}

.dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%;
    background: white;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 5px;
    padding: 10px 0;
    z-index: 100;
    display: none;
}

.user-dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
}

.dropdown-menu a:hover {
    background: #f5f5f5;
}

.dropdown-menu i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.divider {
    height: 1px;
    background: #eee;
    margin: 5px 0;
}

.logout-btn {
    color: #e74c3c;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('stadium-hero.jpg');
    background-size: cover;
    background-position: center;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    padding: 0 20px;
}

.hero-content h2 {
    font-size: 48px;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 700px;
}

.search-box {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    display: flex;
    max-width: 800px;
    margin: 0 auto;
}

.search-input {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px 0 0 5px;
    outline: none;
}

.search-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 0 25px;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

/* Events Section */
.events-section {
    padding: 60px 5%;
}

.section-title {
    text-align: center;
    margin-bottom: 40px;
}

.section-title h2 {
    font-size: 32px;
    color: var(--dark);
    margin-bottom: 10px;
}

.section-title p {
    color: #666;
}

/* Horizontal Events */
.events-horizontal {
    display: flex;
    overflow-x: auto;
    gap: 20px;
    padding: 20px 0;
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
}

.events-horizontal::-webkit-scrollbar {
    height: 8px;
}

.events-horizontal::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.events-horizontal::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.events-horizontal::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Event Card */
.event-card-horizontal {
    min-width: 280px;
    max-width: 280px;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.event-card-horizontal:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.15);
}

.event-image-container {
    height: 160px;
    overflow: hidden;
    position: relative;
}

.event-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.event-card-horizontal:hover .event-image {
    transform: scale(1.05);
}

.event-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.event-content {
    padding: 15px;
}

.event-date {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.event-title {
    font-size: 18px;
    font-weight: bold;
    margin: 10px 0;
    color: #333;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.event-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 10px 0;
    font-size: 14px;
}

.event-meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #555;
}

.event-organizer {
    font-size: 14px;
    color: #777;
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.add-to-cart-btn {
    width: 100%;
    padding: 10px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    margin-top: 15px;
    transition: background 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.add-to-cart-btn:hover {
    background: #45a049;
}

.no-events {
    text-align: center;
    padding: 50px;
    font-size: 18px;
    color: #666;
}

/* Grid Events */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.event-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.event-card:hover {
    transform: translateY(-10px);
}

.event-image {
    height: 200px;
    overflow: hidden;
}

.event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.event-card:hover .event-image img {
    transform: scale(1.1);
}

.event-details {
    padding: 20px;
}

.event-date {
    color: var(--primary);
    font-weight: 600;
    margin-bottom: 10px;
}

.event-title {
    font-size: 20px;
    margin-bottom: 10px;
    color: var(--dark);
}

.event-location {
    color: #666;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.event-location i {
    margin-right: 5px;
}

.event-price {
    font-weight: 600;
    color: var(--secondary);
    margin-bottom: 15px;
}

.book-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    width: 100%;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
}

.book-btn:hover {
    background-color: #e04a1a;
}

/* Features Section */
.features-section {
    background-color: var(--secondary);
    color: white;
    padding: 60px 5%;
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    padding: 30px 20px;
    background-color: rgba(255,255,255,0.1);
    border-radius: 10px;
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 40px;
    margin-bottom: 20px;
    color: var(--primary);
}

.feature-card h3 {
    margin-bottom: 15px;
}

/* Footer */
footer {
    background-color: var(--dark);
    color: white;
    padding: 60px 5% 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-column h3 {
    color: var(--primary);
    margin-bottom: 20px;
    font-size: 18px;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: #ddd;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-column ul li a:hover {
    color: var(--primary);
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    color: white;
    background-color: rgba(255,255,255,0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.social-links a:hover {
    background-color: var(--primary);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255,255,255,0.1);
    color: #aaa;
    font-size: 14px;
}

/* Mobile Menu */
.menu-toggle {
    display: none;
    cursor: pointer;
    font-size: 24px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nav-links {
        display: none;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .navbar {
        flex-wrap: wrap;
    }
    
    .auth-buttons {
        margin-left: auto;
    }
    
    .hero-content h2 {
        font-size: 36px;
    }
}

@media (max-width: 768px) {
    .hero {
        height: 400px;
    }
    
    .hero-content h2 {
        font-size: 28px;
    }
    
    .hero-content p {
        font-size: 16px;
    }
    
    .search-box {
        flex-direction: column;
    }
    
    .search-input {
        border-radius: 5px;
        margin-bottom: 10px;
    }
    
    .search-btn {
        border-radius: 5px;
        padding: 12px;
    }
}
 .cart-icon {
            position: relative;
            cursor: pointer;
            margin-left: 20px;
        }
        
        .cart-count {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ff5722;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .cart-dropdown {
            position: absolute;
            right: 0;
            top: 100%;
            width: 350px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            border-radius: 5px;
            padding: 15px;
            z-index: 1000;
            display: none;
        }
        
        .cart-dropdown.active {
            display: block;
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .cart-total {
            font-weight: bold;
            text-align: right;
            margin-top: 10px;
        }
        
        .checkout-btn {
            display: block;
            width: 100%;
            padding: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            margin-top: 15px;
            cursor: pointer;
        }
        
        .auth-buttons {
            display: flex;
            align-items: center;
        }
        
        /* New styles for two-column event layout */
        .events-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            padding: 20px;
        }
        
        .event-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .event-image img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .event-details {
            padding: 15px;
        }
        
        .event-date {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .event-title {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        
        .event-location {
            color: #666;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .event-price {
            font-weight: bold;
            margin-bottom: 15px;
            color: #2a6496;
        }
        
        .add-to-cart-btn {
            width: 100%;
            padding: 10px;
            background-color: #ff5722;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .add-to-cart-btn:hover {
            background-color: #e64a19;
        }
        
        @media (max-width: 768px) {
            .cart-dropdown {
                width: 280px;
                right: -50px;
            }
            
            .events-grid {
                grid-template-columns: 1fr;
            }
        }