<?php
/**
 * Working Payment Success Page
 * Shows booking confirmation and details
 */

session_start();
require_once 'db_connect.php';

$bookingId = $_GET['booking_id'] ?? '';
$booking = null;
$error = '';

if (empty($bookingId)) {
    $error = "No booking ID provided.";
} else {
    try {
        // Get booking details (with fallback for missing columns)
        try {
            $bookingStmt = $pdo->prepare("
                SELECT b.*, p.Amount, p.Method as PaymentMethod, p.TransactionId, p.PaymentDate
                FROM Bookings b
                LEFT JOIN Payments p ON b.BookingId = p.BookingId
                WHERE b.BookingId = ?
            ");
            $bookingStmt->execute([$bookingId]);
            $booking = $bookingStmt->fetch();
        } catch (Exception $e) {
            // Fallback query for basic table structure
            $bookingStmt = $pdo->prepare("
                SELECT b.*, p.Amount, p.PaymentDate
                FROM Bookings b
                LEFT JOIN Payments p ON b.BookingId = p.BookingId
                WHERE b.BookingId = ?
            ");
            $bookingStmt->execute([$bookingId]);
            $booking = $bookingStmt->fetch();

            // Set default values for missing columns
            if ($booking) {
                $booking['PaymentMethod'] = $booking['PaymentMethod'] ?? 'Cash';
                $booking['TransactionId'] = $booking['TransactionId'] ?? 'N/A';
            }
        }

        if (!$booking) {
            $error = "Booking not found.";
        }

    } catch (Exception $e) {
        $error = "Error retrieving booking: " . $e->getMessage();
    }
}

// Clear cart from localStorage after successful payment
$clearCart = !empty($booking);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 600px; margin: 0 auto; }
        .success-card { background: white; border-radius: 15px; padding: 40px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center; margin-bottom: 30px; }
        .success-icon { font-size: 64px; color: #28a745; margin-bottom: 20px; }
        .error-icon { font-size: 64px; color: #dc3545; margin-bottom: 20px; }
        .success-title { font-size: 28px; font-weight: bold; color: #333; margin-bottom: 15px; }
        .success-message { font-size: 18px; color: #666; margin-bottom: 30px; }
        .booking-details { background: #f8f9fa; border-radius: 10px; padding: 25px; margin: 25px 0; text-align: left; }
        .detail-row { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #dee2e6; }
        .detail-row:last-child { border-bottom: none; }
        .detail-label { font-weight: bold; color: #333; }
        .detail-value { color: #666; }
        .booking-id { font-family: 'Courier New', monospace; font-size: 18px; font-weight: bold; color: #667eea; background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 15px 0; }
        .btn { display: inline-block; padding: 12px 24px; margin: 10px; background: #667eea; color: white; text-decoration: none; border-radius: 8px; transition: background 0.3s ease; font-weight: bold; }
        .btn:hover { background: #5a6fd8; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #5a6268; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .instructions { background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .qr-placeholder { width: 150px; height: 150px; background: #f8f9fa; border: 2px dashed #ddd; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin: 20px auto; color: #666; }
        @media (max-width: 768px) {
            .detail-row { flex-direction: column; align-items: flex-start; gap: 5px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($error): ?>
            <div class="success-card">
                <i class="fas fa-exclamation-circle error-icon"></i>
                <h1 class="success-title">Error</h1>
                <p class="success-message"><?= htmlspecialchars($error) ?></p>
                <a href="Finalll_updated.php" class="btn btn-secondary">
                    <i class="fas fa-home"></i> Back to Home
                </a>
            </div>
        <?php else: ?>
            <div class="success-card">
                <i class="fas fa-check-circle success-icon"></i>
                <h1 class="success-title">Payment Successful!</h1>
                <p class="success-message">Your booking has been confirmed. Thank you for choosing Addis Tickets!</p>
                
                <div class="booking-id">
                    Booking ID: <?= htmlspecialchars($bookingId) ?>
                </div>
                
                <?php if ($booking): ?>
                    <div class="booking-details">
                        <h3 style="margin-top: 0; color: #333; text-align: center;">Booking Details</h3>
                        
                        <div class="detail-row">
                            <span class="detail-label">Booking ID:</span>
                            <span class="detail-value"><?= htmlspecialchars($booking['BookingId']) ?></span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Booking Date:</span>
                            <span class="detail-value"><?= date('F j, Y \a\t g:i A', strtotime($booking['BookingDate'])) ?></span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Payment Status:</span>
                            <span class="detail-value" style="color: #28a745; font-weight: bold;">
                                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($booking['PaymentStatus']) ?>
                            </span>
                        </div>
                        
                        <?php if ($booking['Amount']): ?>
                            <div class="detail-row">
                                <span class="detail-label">Amount Paid:</span>
                                <span class="detail-value" style="font-weight: bold; color: #667eea;">
                                    <?= number_format($booking['Amount'], 2) ?> ETB
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($booking['PaymentMethod']): ?>
                            <div class="detail-row">
                                <span class="detail-label">Payment Method:</span>
                                <span class="detail-value"><?= htmlspecialchars($booking['PaymentMethod']) ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($booking['TransactionId']): ?>
                            <div class="detail-row">
                                <span class="detail-label">Transaction ID:</span>
                                <span class="detail-value" style="font-family: 'Courier New', monospace;">
                                    <?= htmlspecialchars($booking['TransactionId']) ?>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <!-- QR Code Placeholder -->
                <div class="qr-placeholder">
                    <div style="text-align: center;">
                        <i class="fas fa-qrcode" style="font-size: 48px; margin-bottom: 10px;"></i><br>
                        <small>Digital Ticket QR Code</small>
                    </div>
                </div>
                
                <div class="instructions">
                    <h4 style="margin-top: 0; color: #333;"><i class="fas fa-info-circle"></i> Important Instructions</h4>
                    <ul style="text-align: left; margin: 0; padding-left: 20px;">
                        <li>Save this booking confirmation for your records</li>
                        <li>Present your booking ID at the venue entrance</li>
                        <li>Arrive at least 30 minutes before the event starts</li>
                        <li>Bring a valid ID for verification</li>
                        <li>Check your email for the digital ticket</li>
                    </ul>
                </div>
                
                <div style="margin-top: 30px;">
                    <a href="tickets.php" class="btn btn-success">
                        <i class="fas fa-ticket-alt"></i> View My Tickets
                    </a>
                    <a href="Finalll_updated.php" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                </div>
                
                <div style="margin-top: 20px;">
                    <a href="support.php" style="color: #667eea; text-decoration: none;">
                        <i class="fas fa-headset"></i> Need Help? Contact Support
                    </a>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Additional Actions -->
        <div style="text-align: center; margin-top: 20px;">
            <h3 style="color: #333;">What's Next?</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    <i class="fas fa-download" style="font-size: 24px; color: #667eea; margin-bottom: 10px;"></i>
                    <h4 style="margin: 10px 0;">Download Ticket</h4>
                    <p style="color: #666; font-size: 14px;">Get your digital ticket PDF</p>
                </div>
                <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    <i class="fas fa-calendar-plus" style="font-size: 24px; color: #28a745; margin-bottom: 10px;"></i>
                    <h4 style="margin: 10px 0;">Add to Calendar</h4>
                    <p style="color: #666; font-size: 14px;">Don't forget the event date</p>
                </div>
                <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    <i class="fas fa-share-alt" style="font-size: 24px; color: #17a2b8; margin-bottom: 10px;"></i>
                    <h4 style="margin: 10px 0;">Share Event</h4>
                    <p style="color: #666; font-size: 14px;">Tell your friends about it</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        <?php if ($clearCart): ?>
        // Clear cart from localStorage after successful payment
        localStorage.removeItem('cart');
        console.log('Cart cleared after successful payment');
        <?php endif; ?>
        
        // Auto-redirect after 30 seconds (optional)
        setTimeout(function() {
            if (confirm('Would you like to view your tickets now?')) {
                window.location.href = 'tickets.php';
            }
        }, 30000);
        
        // Print functionality
        function printBooking() {
            window.print();
        }
        
        // Add print button
        document.addEventListener('DOMContentLoaded', function() {
            const printBtn = document.createElement('button');
            printBtn.innerHTML = '<i class="fas fa-print"></i> Print Confirmation';
            printBtn.className = 'btn btn-secondary';
            printBtn.onclick = printBooking;
            
            const buttonContainer = document.querySelector('.success-card > div:last-child');
            if (buttonContainer) {
                buttonContainer.appendChild(printBtn);
            }
        });
        
        console.log('Payment success page loaded');
        console.log('Booking ID: <?= htmlspecialchars($bookingId) ?>');
    </script>
</body>
</html>
