<?php
session_start();
header('Content-Type: application/json');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'EventBB');
define('DB_USER', 'secure_user');
define('DB_PASS', '');

// Error handling
ini_set('display_errors', 0);
error_reporting(0);

if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    error_log('Database connection failed: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$userId = $_SESSION['userId'];

try {
    $pdo->beginTransaction();

    foreach ($data as $item) {
        // Verify ticket availability
        $ticketStmt = $pdo->prepare("
            SELECT TicketId, Status
            FROM Tickets
            WHERE TicketId = ?
            AND Status = 'Available'
            FOR UPDATE
        ");
        $ticketStmt->execute([$item['ticketId']]);
        $ticket = $ticketStmt->fetch();

        if (!$ticket) {
            throw new Exception("Ticket {$item['ticketId']} not available");
        }

        // Update ticket status
        $updateTicket = $pdo->prepare("
            UPDATE Tickets
            SET Status = 'Reserved'
            WHERE TicketId = ?
        ");
        $updateTicket->execute([$item['ticketId']]);

        // Create booking
        $bookingId = 'B' . uniqid();
        $bookingStmt = $pdo->prepare("
            INSERT INTO Bookings (
                BookingId,
                UserId,
                TicketId,
                PaymentStatus
            ) VALUES (?, ?, ?, 'Completed')
        ");
        $bookingStmt->execute([$bookingId, $userId, $item['ticketId']]);

        // Create payment record
        $paymentId = 'P' . uniqid();
        $paymentStmt = $pdo->prepare("
            INSERT INTO Payments (
                PaymentId,
                BookingId,
                Amount,
                Method,
                Status
            ) VALUES (?, ?, ?, ?, 'Completed')
        ");
        $paymentStmt->execute([
            $paymentId,
            $bookingId,
            $item['price'],
            $item['paymentMethod'] // Should be validated from allowed methods
        ]);
    }

    $pdo->commit();

    // Clear the cart
    $_SESSION['cart'] = [];

    echo json_encode([
        'success' => true,
        'message' => 'Checkout completed successfully'
    ]);

} catch (Exception $e) {
    $pdo->rollBack();
    error_log('Checkout error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Checkout failed: ' . $e->getMessage()
    ]);
}