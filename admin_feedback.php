<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if user is admin - SECURE VERSION
if (!isset($_SESSION['admin_loggedin']) || $_SESSION['admin_loggedin'] !== true) {
    // Redirect to admin login if not authenticated
    header("Location: admin_login.php");
    exit();
}

$message = '';
$error = '';

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Security validation failed. Please try again.";
    } else {
        try {
            if ($_POST['action'] === 'update_status') {
                $ticketId = sanitizeInput($_POST['ticket_id']);
                $status = sanitizeInput($_POST['status']);
                $response = sanitizeInput($_POST['response'] ?? '');
                
                $stmt = $pdo->prepare("
                    UPDATE SupportTickets 
                    SET Status = ?, UpdatedDate = NOW() 
                    WHERE TicketId = ?
                ");
                $stmt->execute([$status, $ticketId]);
                
                // If there's a response, you could add it to a responses table
                // For now, we'll just update the status
                
                $message = "Feedback status updated successfully!";
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get feedback statistics
try {
    $statsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_feedback,
            COUNT(CASE WHEN Status = 'Open' THEN 1 END) as open_feedback,
            COUNT(CASE WHEN Status = 'In Progress' THEN 1 END) as in_progress,
            COUNT(CASE WHEN Status = 'Resolved' THEN 1 END) as resolved,
            COUNT(CASE WHEN Category = 'feedback' THEN 1 END) as general_feedback,
            AVG(CASE WHEN Message LIKE '%Rating: %' THEN 
                CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(Message, 'Rating: ', -1), '/', 1) AS UNSIGNED) 
                ELSE NULL END) as avg_rating
        FROM SupportTickets 
        WHERE Category IN ('feedback', 'general', 'booking', 'technical', 'payment')
    ");
    $statsStmt->execute();
    $stats = $statsStmt->fetch();
} catch (Exception $e) {
    $stats = ['total_feedback' => 0, 'open_feedback' => 0, 'in_progress' => 0, 'resolved' => 0, 'general_feedback' => 0, 'avg_rating' => 0];
}

// Check if Name column exists in SupportTickets table
try {
    $checkStmt = $pdo->query("SHOW COLUMNS FROM SupportTickets LIKE 'Name'");
    $nameColumnExists = $checkStmt->rowCount() > 0;
} catch (Exception $e) {
    $nameColumnExists = false;
}

// Get feedback list with filters
$filter = $_GET['filter'] ?? 'all';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';

try {
    if ($nameColumnExists) {
        $sql = "
            SELECT st.*,
                   CASE WHEN st.Message LIKE '%Rating: %' THEN
                       CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(st.Message, 'Rating: ', -1), '/', 1) AS UNSIGNED)
                       ELSE NULL END as rating,
                   CASE WHEN st.Message LIKE '%Email: %' THEN
                       SUBSTRING_INDEX(SUBSTRING_INDEX(st.Message, 'Email: ', -1), CHAR(10), 1)
                       ELSE 'No email provided' END as Email
            FROM SupportTickets st
            WHERE 1=1
        ";
    } else {
        $sql = "
            SELECT st.*,
                   CASE WHEN st.Message LIKE '%Rating: %' THEN
                       CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(st.Message, 'Rating: ', -1), '/', 1) AS UNSIGNED)
                       ELSE NULL END as rating,
                   CASE WHEN st.Message LIKE 'Name: %' THEN
                       SUBSTRING_INDEX(SUBSTRING_INDEX(st.Message, 'Name: ', -1), CHAR(10), 1)
                       ELSE 'Anonymous' END as Name,
                   CASE WHEN st.Message LIKE '%Email: %' THEN
                       SUBSTRING_INDEX(SUBSTRING_INDEX(st.Message, 'Email: ', -1), CHAR(10), 1)
                       ELSE 'No email provided' END as Email
            FROM SupportTickets st
            WHERE 1=1
        ";
    }
    
    $params = [];
    
    if ($filter === 'feedback') {
        $sql .= " AND st.Category IN ('feedback', 'general')";
    } elseif ($filter === 'recent') {
        $sql .= " AND st.CreatedDate >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    }
    
    if ($category) {
        $sql .= " AND st.Category = ?";
        $params[] = $category;
    }
    
    if ($status) {
        $sql .= " AND st.Status = ?";
        $params[] = $status;
    }
    
    $sql .= " ORDER BY st.CreatedDate DESC LIMIT 50";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $feedbackList = $stmt->fetchAll();
} catch (Exception $e) {
    $feedbackList = [];
    $error = "Error loading feedback: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Management - Admin Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header-content { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { display: flex; align-items: center; gap: 10px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; font-weight: 600; transition: all 0.3s ease; }
        .btn-secondary { background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); }
        .btn-secondary:hover { background: rgba(255,255,255,0.3); }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .container { max-width: 1200px; margin: 0 auto; padding: 30px 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; border-radius: 10px; padding: 25px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-card .icon { font-size: 2.5em; margin-bottom: 15px; }
        .stat-card .value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-card .label { color: #666; font-size: 14px; }
        .filters { background: white; border-radius: 10px; padding: 20px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end; }
        .filter-group { display: flex; flex-direction: column; }
        .filter-group label { margin-bottom: 5px; font-weight: 600; }
        .filter-group select { padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .feedback-list { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .feedback-item { border-bottom: 1px solid #eee; padding: 20px; }
        .feedback-item:last-child { border-bottom: none; }
        .feedback-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }
        .feedback-meta { display: flex; gap: 15px; align-items: center; margin-bottom: 10px; }
        .feedback-meta span { font-size: 14px; color: #666; }
        .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; }
        .status-open { background: #fff3cd; color: #856404; }
        .status-in-progress { background: #cce5ff; color: #004085; }
        .status-resolved { background: #d4edda; color: #155724; }
        .status-closed { background: #f8d7da; color: #721c24; }
        .priority-high { color: #dc3545; }
        .priority-medium { color: #ffc107; }
        .priority-low { color: #28a745; }
        .rating-stars { color: #ffd700; }
        .feedback-content { margin: 15px 0; }
        .feedback-actions { display: flex; gap: 10px; margin-top: 15px; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 5% auto; padding: 30px; width: 90%; max-width: 600px; border-radius: 10px; }
        .close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close:hover { color: black; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; }
        .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        @media (max-width: 768px) { .feedback-header { flex-direction: column; gap: 10px; } .feedback-actions { flex-wrap: wrap; } }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-comments"></i> Feedback Management</h1>
            <div>
                <a href="admin_dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="feedback.php" class="btn btn-secondary">
                    <i class="fas fa-plus"></i> View Feedback Form
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon" style="color: #667eea;"><i class="fas fa-comments"></i></div>
                <div class="value" style="color: #667eea;"><?= number_format($stats['total_feedback']) ?></div>
                <div class="label">Total Feedback</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #ffc107;"><i class="fas fa-clock"></i></div>
                <div class="value" style="color: #ffc107;"><?= number_format($stats['open_feedback']) ?></div>
                <div class="label">Open Items</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #17a2b8;"><i class="fas fa-cog"></i></div>
                <div class="value" style="color: #17a2b8;"><?= number_format($stats['in_progress']) ?></div>
                <div class="label">In Progress</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #28a745;"><i class="fas fa-check"></i></div>
                <div class="value" style="color: #28a745;"><?= number_format($stats['resolved']) ?></div>
                <div class="label">Resolved</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #ffd700;"><i class="fas fa-star"></i></div>
                <div class="value" style="color: #ffd700;"><?= number_format($stats['avg_rating'], 1) ?></div>
                <div class="label">Avg Rating</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <form method="GET">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="filter">Filter</label>
                        <select id="filter" name="filter" onchange="this.form.submit()">
                            <option value="all" <?= $filter === 'all' ? 'selected' : '' ?>>All Feedback</option>
                            <option value="feedback" <?= $filter === 'feedback' ? 'selected' : '' ?>>Feedback Only</option>
                            <option value="recent" <?= $filter === 'recent' ? 'selected' : '' ?>>Recent (7 days)</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="category">Category</label>
                        <select id="category" name="category" onchange="this.form.submit()">
                            <option value="">All Categories</option>
                            <option value="feedback" <?= $category === 'feedback' ? 'selected' : '' ?>>General Feedback</option>
                            <option value="booking" <?= $category === 'booking' ? 'selected' : '' ?>>Booking</option>
                            <option value="technical" <?= $category === 'technical' ? 'selected' : '' ?>>Technical</option>
                            <option value="payment" <?= $category === 'payment' ? 'selected' : '' ?>>Payment</option>
                            <option value="general" <?= $category === 'general' ? 'selected' : '' ?>>Suggestions</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="Open" <?= $status === 'Open' ? 'selected' : '' ?>>Open</option>
                            <option value="In Progress" <?= $status === 'In Progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="Resolved" <?= $status === 'Resolved' ? 'selected' : '' ?>>Resolved</option>
                            <option value="Closed" <?= $status === 'Closed' ? 'selected' : '' ?>>Closed</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <a href="?" class="btn btn-secondary">Clear Filters</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Feedback List -->
        <div class="feedback-list">
            <?php if (empty($feedbackList)): ?>
                <div style="text-align: center; padding: 40px;">
                    <i class="fas fa-comments" style="font-size: 4em; color: #ddd; margin-bottom: 20px;"></i>
                    <h3>No Feedback Found</h3>
                    <p>No feedback matches your current filters.</p>
                </div>
            <?php else: ?>
                <?php foreach ($feedbackList as $feedback): ?>
                    <div class="feedback-item">
                        <div class="feedback-header">
                            <div>
                                <h3><?= htmlspecialchars($feedback['Subject']) ?></h3>
                                <div class="feedback-meta">
                                    <span><i class="fas fa-user"></i> <?= htmlspecialchars($feedback['Name'] ?? 'Anonymous') ?></span>
                                    <span><i class="fas fa-envelope"></i> <?= htmlspecialchars($feedback['Email'] ?? 'No email provided') ?></span>
                                    <span><i class="fas fa-calendar"></i> <?= date('M d, Y H:i', strtotime($feedback['CreatedDate'])) ?></span>
                                    <span><i class="fas fa-tag"></i> <?= ucfirst($feedback['Category']) ?></span>
                                    <?php if ($feedback['rating']): ?>
                                        <span class="rating-stars">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star" style="color: <?= $i <= $feedback['rating'] ? '#ffd700' : '#ddd' ?>;"></i>
                                            <?php endfor; ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div>
                                <span class="status-badge status-<?= strtolower(str_replace(' ', '-', $feedback['Status'])) ?>">
                                    <?= $feedback['Status'] ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="feedback-content">
                            <p><?= nl2br(htmlspecialchars($feedback['Message'])) ?></p>
                        </div>
                        
                        <div class="feedback-actions">
                            <button class="btn btn-primary" onclick="updateStatus('<?= $feedback['TicketId'] ?>', '<?= $feedback['Status'] ?>')">
                                <i class="fas fa-edit"></i> Update Status
                            </button>
                            <?php if ($feedback['Status'] === 'Open'): ?>
                                <button class="btn btn-warning" onclick="quickUpdate('<?= $feedback['TicketId'] ?>', 'In Progress')">
                                    <i class="fas fa-play"></i> Start Progress
                                </button>
                            <?php endif; ?>
                            <?php if ($feedback['Status'] !== 'Resolved'): ?>
                                <button class="btn btn-success" onclick="quickUpdate('<?= $feedback['TicketId'] ?>', 'Resolved')">
                                    <i class="fas fa-check"></i> Mark Resolved
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Update Feedback Status</h2>
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="ticket_id" id="modalTicketId">
                
                <div class="form-group">
                    <label for="modalStatus">Status</label>
                    <select id="modalStatus" name="status" required>
                        <option value="Open">Open</option>
                        <option value="In Progress">In Progress</option>
                        <option value="Resolved">Resolved</option>
                        <option value="Closed">Closed</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="modalResponse">Response (Optional)</label>
                    <textarea id="modalResponse" name="response" rows="4" placeholder="Add a response or note..."></textarea>
                </div>
                
                <button type="submit" class="btn btn-primary">Update Status</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </form>
        </div>
    </div>

    <script>
        function updateStatus(ticketId, currentStatus) {
            document.getElementById('modalTicketId').value = ticketId;
            document.getElementById('modalStatus').value = currentStatus;
            document.getElementById('statusModal').style.display = 'block';
        }
        
        function quickUpdate(ticketId, newStatus) {
            if (confirm(`Are you sure you want to mark this feedback as "${newStatus}"?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="ticket_id" value="${ticketId}">
                    <input type="hidden" name="status" value="${newStatus}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function closeModal() {
            document.getElementById('statusModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('statusModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
        
        // Close modal with X button
        document.querySelector('.close').onclick = closeModal;
    </script>
</body>
</html>
