<?php
session_start();

$email = $_GET['email'] ?? '';
$emailError = isset($_GET['email_error']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Successful - Addis Tickets</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <link href="styleFinal.css" rel="stylesheet"/>
    <style>
        .success-container {
            max-width: 600px;
            margin: 100px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 20px;
        }
        .message {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .email-highlight {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
        }
        .btn {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .steps {
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <i class="fas fa-check-circle success-icon"></i>
        <h2>Registration Successful!</h2>

        <p class="message">
            Thank you for joining Addis Tickets! Your account has been created successfully.
        </p>

        <?php if (!empty($email)): ?>
        <div class="email-highlight">
            <i class="fas fa-envelope"></i> <?= htmlspecialchars($email) ?>
        </div>

        <?php if ($emailError): ?>
            <div class="warning-box">
                <p><strong>Note:</strong> There was an issue sending the verification email. You can resend it below.</p>
            </div>
        <?php else: ?>
            <div class="steps">
                <h4><i class="fas fa-list-check"></i> Next Steps:</h4>
                <ol>
                    <li><strong>Check your email</strong> - We've sent a verification link</li>
                    <li><strong>Click the verification link</strong> - This will activate your account</li>
                    <li><strong>Start booking events</strong> - Once verified, you can book tickets</li>
                </ol>
            </div>
        <?php endif; ?>

        <div style="margin-top: 30px;">
            <a href="resend_verification.php?email=<?= urlencode($email) ?>" class="btn">
                <i class="fas fa-envelope"></i> Resend Verification
            </a>
        </div>
        <?php endif; ?>

        <div style="margin-top: 20px;">
            <a href="login.php" class="btn">
                <i class="fas fa-sign-in-alt"></i> Go to Login
            </a>
            <a href="Finalll_updated.php" class="btn">
                <i class="fas fa-home"></i> Back to Home
            </a>
        </div>
    </div>
</body>
</html>