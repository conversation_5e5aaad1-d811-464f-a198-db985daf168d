<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if organizer is logged in
if (!isset($_SESSION['organizer_id'])) {
    header("Location: organizer_login.php");
    exit();
}

$organizerId = $_SESSION['organizer_id'];
$organizerName = $_SESSION['organizer_name'];

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate and sanitize input
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $eventDate = $_POST['event_date'] ?? '';
        $endDate = $_POST['end_date'] ?? '';
        $place = trim($_POST['place'] ?? '');
        $address = trim($_POST['address'] ?? '');
        $category = $_POST['category'] ?? '';
        $maxCapacity = (int)($_POST['max_capacity'] ?? 1000);
        $ticketPrice = (float)($_POST['ticket_price'] ?? 0);

        // Get seat allocation
        $regularSeats = (int)($_POST['regular_seats'] ?? 0);
        $vipSeats = (int)($_POST['vip_seats'] ?? 0);
        $vvipSeats = (int)($_POST['vvip_seats'] ?? 0);
        $totalAllocatedSeats = $regularSeats + $vipSeats + $vvipSeats;
        
        // Validation
        if (empty($title) || empty($eventDate) || empty($place) || empty($category)) {
            $error = "Please fill in all required fields.";
        } elseif (strtotime($eventDate) <= time()) {
            $error = "Event date must be in the future.";
        } elseif (!empty($endDate) && strtotime($endDate) <= strtotime($eventDate)) {
            $error = "End date must be after start date.";
        } elseif ($totalAllocatedSeats > $maxCapacity) {
            $error = "Total allocated seats ($totalAllocatedSeats) cannot exceed maximum capacity ($maxCapacity).";
        } elseif ($totalAllocatedSeats <= 0) {
            $error = "Please allocate at least some seats for the event.";
        } else {
            // Generate event ID
            $eventId = 'EVT' . substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 6);
            
            // Handle image upload
            $eventImage = null;
            if (isset($_FILES['event_image']) && $_FILES['event_image']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = 'uploads/events/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $fileExtension = strtolower(pathinfo($_FILES['event_image']['name'], PATHINFO_EXTENSION));
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
                
                if (in_array($fileExtension, $allowedExtensions)) {
                    $fileName = uniqid() . '_' . $_FILES['event_image']['name'];
                    $uploadPath = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($_FILES['event_image']['tmp_name'], $uploadPath)) {
                        $eventImage = $uploadPath;
                    }
                }
            }
            
            // Insert event into database
            $stmt = $pdo->prepare("
                INSERT INTO Events (
                    EventId, Title, Description, EventDate, EndDate, Place, Address, 
                    EventCategory, MaxCapacity, TicketPrice, EventImage, OrganizerId, 
                    IsApproved, CreatedDate
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, NOW())
            ");
            
            $result = $stmt->execute([
                $eventId, $title, $description, $eventDate, $endDate, $place, $address,
                $category, $maxCapacity, $ticketPrice, $eventImage, $organizerId
            ]);
            
            if ($result) {
                // Create tickets based on organizer's allocation
                $ticketTypes = [];

                if ($regularSeats > 0) {
                    $ticketTypes[] = ['type' => 'Regular', 'price' => $ticketPrice, 'quantity' => $regularSeats];
                }
                if ($vipSeats > 0) {
                    $ticketTypes[] = ['type' => 'VIP', 'price' => $ticketPrice * 1.5, 'quantity' => $vipSeats];
                }
                if ($vvipSeats > 0) {
                    $ticketTypes[] = ['type' => 'VVIP', 'price' => $ticketPrice * 2, 'quantity' => $vvipSeats];
                }
                
                foreach ($ticketTypes as $ticketType) {
                    if ($ticketType['quantity'] > 0) {
                        $ticketId = 'TCK' . substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 6);
                        $ticketStmt = $pdo->prepare("
                            INSERT INTO Tickets (TicketId, EventId, TicketType, Price, AvailableTickets, Status) 
                            VALUES (?, ?, ?, ?, ?, 'Available')
                        ");
                        $ticketStmt->execute([$ticketId, $eventId, $ticketType['type'], $ticketType['price'], $ticketType['quantity']]);
                    }
                }
                
                $message = "Event created successfully! It will be reviewed by admin before being published.";
                
                // Log the event creation
                logSecurityEvent('event_created', [
                    'event_id' => $eventId,
                    'organizer_id' => $organizerId,
                    'title' => $title
                ]);
                
                // Clear form data
                $_POST = [];
            } else {
                $error = "Failed to create event. Please try again.";
            }
        }
    } catch (Exception $e) {
        $error = "Error creating event: " . $e->getMessage();
        error_log("Event creation error: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Event - Organizer Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn-secondary:hover {
            background: rgba(255,255,255,0.3);
        }
        .btn-primary {
            background: #28a745;
            color: white;
        }
        .btn-primary:hover {
            background: #218838;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .form-header {
            background: #f8f9fa;
            padding: 25px;
            border-bottom: 1px solid #e9ecef;
        }
        .form-header h2 {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #333;
        }
        .form-content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .required {
            color: #dc3545;
        }
        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }
        .file-upload input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .file-upload-label {
            display: block;
            padding: 12px;
            border: 2px dashed #e1e5e9;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .file-upload:hover .file-upload-label {
            border-color: #667eea;
            background: #f8f9ff;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-plus-circle"></i> Create New Event</h1>
            <a href="organizer_dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h2><i class="fas fa-calendar-plus"></i> Event Details</h2>
                <p style="margin-top: 10px; color: #666;">Fill in the information below to create your event. All events require admin approval before being published.</p>
            </div>
            
            <div class="form-content">
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="title">Event Title <span class="required">*</span></label>
                        <input type="text" id="title" name="title" required maxlength="200"
                               value="<?= htmlspecialchars($_POST['title'] ?? '') ?>"
                               placeholder="Enter event title">
                    </div>

                    <div class="form-group">
                        <label for="description">Event Description</label>
                        <textarea id="description" name="description" maxlength="1000"
                                  placeholder="Describe your event..."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="event_date">Start Date & Time <span class="required">*</span></label>
                            <input type="datetime-local" id="event_date" name="event_date" required
                                   value="<?= $_POST['event_date'] ?? '' ?>">
                        </div>
                        <div class="form-group">
                            <label for="end_date">End Date & Time</label>
                            <input type="datetime-local" id="end_date" name="end_date"
                                   value="<?= $_POST['end_date'] ?? '' ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="place">Venue Name <span class="required">*</span></label>
                        <input type="text" id="place" name="place" required maxlength="200"
                               value="<?= htmlspecialchars($_POST['place'] ?? '') ?>"
                               placeholder="e.g., Addis Ababa Stadium">
                    </div>

                    <div class="form-group">
                        <label for="address">Venue Address</label>
                        <input type="text" id="address" name="address" maxlength="300"
                               value="<?= htmlspecialchars($_POST['address'] ?? '') ?>"
                               placeholder="Full address of the venue">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="category">Event Category <span class="required">*</span></label>
                            <select id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Music" <?= ($_POST['category'] ?? '') === 'Music' ? 'selected' : '' ?>>Music</option>
                                <option value="Sports" <?= ($_POST['category'] ?? '') === 'Sports' ? 'selected' : '' ?>>Sports</option>
                                <option value="Culture" <?= ($_POST['category'] ?? '') === 'Culture' ? 'selected' : '' ?>>Culture</option>
                                <option value="Technology" <?= ($_POST['category'] ?? '') === 'Technology' ? 'selected' : '' ?>>Technology</option>
                                <option value="Art" <?= ($_POST['category'] ?? '') === 'Art' ? 'selected' : '' ?>>Art</option>
                                <option value="Business" <?= ($_POST['category'] ?? '') === 'Business' ? 'selected' : '' ?>>Business</option>
                                <option value="Education" <?= ($_POST['category'] ?? '') === 'Education' ? 'selected' : '' ?>>Education</option>
                                <option value="Other" <?= ($_POST['category'] ?? '') === 'Other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="max_capacity">Maximum Capacity</label>
                            <input type="number" id="max_capacity" name="max_capacity" min="1" max="100000"
                                   value="<?= $_POST['max_capacity'] ?? '1000' ?>"
                                   placeholder="1000">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="ticket_price">Base Ticket Price (ETB)</label>
                        <input type="number" id="ticket_price" name="ticket_price" min="0" step="0.01"
                               value="<?= $_POST['ticket_price'] ?? '0' ?>"
                               placeholder="0.00">
                        <small style="color: #666; font-size: 14px;">Base price for Regular tickets. VIP = 1.5x, VVIP = 2x this price</small>
                    </div>

                    <h3 style="margin-top: 30px; margin-bottom: 20px; color: #333;"><i class="fas fa-chair"></i> Seat Allocation</h3>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <p style="color: #666; margin-bottom: 15px;">Specify how many seats to allocate for each ticket type. Total must not exceed maximum capacity.</p>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="regular_seats">Regular Seats</label>
                                <input type="number" id="regular_seats" name="regular_seats" min="0"
                                       value="<?= $_POST['regular_seats'] ?? '700' ?>"
                                       placeholder="700">
                                <small style="color: #666;">Price: Base price</small>
                            </div>
                            <div class="form-group">
                                <label for="vip_seats">VIP Seats</label>
                                <input type="number" id="vip_seats" name="vip_seats" min="0"
                                       value="<?= $_POST['vip_seats'] ?? '200' ?>"
                                       placeholder="200">
                                <small style="color: #666;">Price: 1.5x base price</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="vvip_seats">VVIP Seats</label>
                                <input type="number" id="vvip_seats" name="vvip_seats" min="0"
                                       value="<?= $_POST['vvip_seats'] ?? '100' ?>"
                                       placeholder="100">
                                <small style="color: #666;">Price: 2x base price</small>
                            </div>
                            <div class="form-group">
                                <label>Total Allocated</label>
                                <input type="text" id="total_allocated" readonly
                                       style="background: #e9ecef; font-weight: bold;"
                                       value="1000">
                                <small style="color: #666;">Must not exceed max capacity</small>
                            </div>
                        </div>

                        <div id="allocation_warning" style="display: none; background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin-top: 10px;">
                            <i class="fas fa-exclamation-triangle"></i> <span id="warning_text"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="event_image">Event Image</label>
                        <div class="file-upload">
                            <input type="file" id="event_image" name="event_image" accept="image/*">
                            <label for="event_image" class="file-upload-label">
                                <i class="fas fa-cloud-upload-alt" style="font-size: 2em; color: #667eea; margin-bottom: 10px;"></i><br>
                                Click to upload event image<br>
                                <small style="color: #666;">JPG, PNG, GIF up to 5MB</small>
                            </label>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn btn-primary" style="padding: 15px 40px; font-size: 16px;">
                            <i class="fas fa-save"></i> Create Event
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Set minimum date to today
        document.getElementById('event_date').min = new Date().toISOString().slice(0, 16);
        document.getElementById('end_date').min = new Date().toISOString().slice(0, 16);

        // Update end date minimum when start date changes
        document.getElementById('event_date').addEventListener('change', function() {
            document.getElementById('end_date').min = this.value;
        });

        // File upload preview
        document.getElementById('event_image').addEventListener('change', function() {
            const label = document.querySelector('.file-upload-label');
            if (this.files.length > 0) {
                label.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i><br>File selected: ' + this.files[0].name;
            }
        });

        // Seat allocation calculations
        function updateSeatAllocation() {
            const maxCapacity = parseInt(document.getElementById('max_capacity').value) || 0;
            const regularSeats = parseInt(document.getElementById('regular_seats').value) || 0;
            const vipSeats = parseInt(document.getElementById('vip_seats').value) || 0;
            const vvipSeats = parseInt(document.getElementById('vvip_seats').value) || 0;
            const totalAllocated = regularSeats + vipSeats + vvipSeats;

            // Update total display
            document.getElementById('total_allocated').value = totalAllocated.toLocaleString();

            // Show warnings
            const warningDiv = document.getElementById('allocation_warning');
            const warningText = document.getElementById('warning_text');

            if (totalAllocated > maxCapacity) {
                warningDiv.style.display = 'block';
                warningDiv.style.background = '#f8d7da';
                warningDiv.style.color = '#721c24';
                warningText.textContent = `Total allocated seats (${totalAllocated}) exceeds maximum capacity (${maxCapacity})`;
            } else if (totalAllocated === 0) {
                warningDiv.style.display = 'block';
                warningDiv.style.background = '#fff3cd';
                warningDiv.style.color = '#856404';
                warningText.textContent = 'Please allocate at least some seats for the event';
            } else if (totalAllocated < maxCapacity) {
                const remaining = maxCapacity - totalAllocated;
                warningDiv.style.display = 'block';
                warningDiv.style.background = '#d1ecf1';
                warningDiv.style.color = '#0c5460';
                warningText.textContent = `${remaining} seats remaining from maximum capacity`;
            } else {
                warningDiv.style.display = 'none';
            }

            // Update pricing preview
            updatePricingPreview();
        }

        function updatePricingPreview() {
            const basePrice = parseFloat(document.getElementById('ticket_price').value) || 0;
            const regularSeats = parseInt(document.getElementById('regular_seats').value) || 0;
            const vipSeats = parseInt(document.getElementById('vip_seats').value) || 0;
            const vvipSeats = parseInt(document.getElementById('vvip_seats').value) || 0;

            if (basePrice > 0) {
                // Update price displays
                const regularPriceText = document.querySelector('input[name="regular_seats"]').nextElementSibling;
                const vipPriceText = document.querySelector('input[name="vip_seats"]').nextElementSibling;
                const vvipPriceText = document.querySelector('input[name="vvip_seats"]').nextElementSibling;

                if (regularPriceText) regularPriceText.textContent = `Price: ${basePrice.toLocaleString()} ETB each`;
                if (vipPriceText) vipPriceText.textContent = `Price: ${(basePrice * 1.5).toLocaleString()} ETB each`;
                if (vvipPriceText) vvipPriceText.textContent = `Price: ${(basePrice * 2).toLocaleString()} ETB each`;
            }
        }

        // Auto-distribute seats when max capacity changes
        function autoDistributeSeats() {
            const maxCapacity = parseInt(document.getElementById('max_capacity').value) || 1000;

            // Default distribution: 70% Regular, 20% VIP, 10% VVIP
            const regularSeats = Math.floor(maxCapacity * 0.7);
            const vipSeats = Math.floor(maxCapacity * 0.2);
            const vvipSeats = Math.floor(maxCapacity * 0.1);

            document.getElementById('regular_seats').value = regularSeats;
            document.getElementById('vip_seats').value = vipSeats;
            document.getElementById('vvip_seats').value = vvipSeats;

            updateSeatAllocation();
        }

        // Event listeners
        document.getElementById('max_capacity').addEventListener('input', updateSeatAllocation);
        document.getElementById('regular_seats').addEventListener('input', updateSeatAllocation);
        document.getElementById('vip_seats').addEventListener('input', updateSeatAllocation);
        document.getElementById('vvip_seats').addEventListener('input', updateSeatAllocation);
        document.getElementById('ticket_price').addEventListener('input', updatePricingPreview);

        // Add auto-distribute button
        document.addEventListener('DOMContentLoaded', function() {
            const maxCapacityGroup = document.getElementById('max_capacity').parentElement;
            const autoBtn = document.createElement('button');
            autoBtn.type = 'button';
            autoBtn.className = 'btn btn-secondary';
            autoBtn.style.marginTop = '10px';
            autoBtn.style.padding = '8px 15px';
            autoBtn.style.fontSize = '14px';
            autoBtn.innerHTML = '<i class="fas fa-magic"></i> Auto-Distribute Seats';
            autoBtn.onclick = autoDistributeSeats;
            maxCapacityGroup.appendChild(autoBtn);

            // Initial calculation
            updateSeatAllocation();
        });
    </script>
</body>
</html>
