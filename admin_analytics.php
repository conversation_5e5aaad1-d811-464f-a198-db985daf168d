<?php
session_start();
require_once 'db_connect.php';
require_once 'security_config.php';

// Check if user is admin - SECURE VERSION
if (!isset($_SESSION['admin_loggedin']) || $_SESSION['admin_loggedin'] !== true) {
    // Redirect to admin login if not authenticated
    header("Location: admin_login.php");
    exit();
}

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get date range for filtering
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // Today
$period = $_GET['period'] ?? 'month';

try {
    // 1. SALES ANALYTICS (with error handling)
    try {
        $salesQuery = "
            SELECT
                DATE(b.BookingDate) as booking_date,
                COUNT(b.BookingId) as total_bookings,
                COALESCE(SUM(p.Amount), 0) as total_revenue,
                COALESCE(AVG(p.Amount), 0) as avg_booking_value
            FROM Bookings b
            LEFT JOIN Payments p ON b.BookingId = p.BookingId
            WHERE b.BookingDate BETWEEN ? AND ?
            GROUP BY DATE(b.BookingDate)
            ORDER BY booking_date DESC
        ";
        $salesStmt = $pdo->prepare($salesQuery);
        $salesStmt->execute([$startDate, $endDate]);
        $salesData = $salesStmt->fetchAll();
    } catch (Exception $e) {
        // Fallback with basic data
        $salesData = [];
        error_log("Analytics sales query failed: " . $e->getMessage());
    }

    // 2. EVENT PERFORMANCE (simplified query)
    try {
        $eventQuery = "
            SELECT
                e.EventId,
                e.Title,
                e.EventDate,
                e.Place,
                COUNT(DISTINCT b.BookingId) as total_bookings,
                COALESCE(SUM(p.Amount), 0) as event_revenue,
                COUNT(DISTINCT b.UserId) as unique_customers,
                COALESCE(o.OrganizerName, 'Unknown') as OrganizerName
            FROM Events e
            LEFT JOIN Bookings b ON e.EventId = b.EventId
            LEFT JOIN Payments p ON b.BookingId = p.BookingId
            LEFT JOIN EventOrganizers o ON e.OrganizerId = o.OrganizerId
            WHERE e.EventDate BETWEEN ? AND ? AND e.IsApproved = 1
            GROUP BY e.EventId, e.Title, e.EventDate, e.Place, o.OrganizerName
            ORDER BY event_revenue DESC
            LIMIT 20
        ";
        $eventStmt = $pdo->prepare($eventQuery);
        $eventStmt->execute([$startDate, $endDate]);
        $eventData = $eventStmt->fetchAll();
    } catch (Exception $e) {
        error_log("Event analytics query failed: " . $e->getMessage());
        $eventData = [];
    }

    // 3. USER ANALYTICS (using BookingDate as proxy for user activity since no RegistrationDate exists)
    $userData = [];
    try {
        $userQuery = "
            SELECT
                DATE(b.BookingDate) as activity_date,
                COUNT(DISTINCT b.UserId) as active_users
            FROM Bookings b
            WHERE b.BookingDate BETWEEN ? AND ?
            GROUP BY DATE(b.BookingDate)
            ORDER BY activity_date DESC
            LIMIT 10
        ";
        $userStmt = $pdo->prepare($userQuery);
        $userStmt->execute([$startDate, $endDate]);
        $userData = $userStmt->fetchAll();
    } catch (Exception $e) {
        error_log("Failed to get user analytics: " . $e->getMessage());
        // Fallback: create sample data for chart
        $userData = [
            ['activity_date' => date('Y-m-d'), 'active_users' => 0],
            ['activity_date' => date('Y-m-d', strtotime('-1 day')), 'active_users' => 0],
            ['activity_date' => date('Y-m-d', strtotime('-2 days')), 'active_users' => 0]
        ];
    }

    // 4. SUMMARY STATISTICS (with individual error handling)
    $summary = [
        'total_users' => 0,
        'total_events' => 0,
        'period_bookings' => 0,
        'period_revenue' => 0,
        'pending_events' => 0
    ];

    try {
        // Get total users
        $userCountStmt = $pdo->query("SELECT COUNT(*) as count FROM Users");
        $userCount = $userCountStmt->fetch();
        $summary['total_users'] = $userCount['count'] ?? 0;
    } catch (Exception $e) {
        error_log("Failed to get user count: " . $e->getMessage());
    }

    try {
        // Get total events
        $eventCountStmt = $pdo->query("SELECT COUNT(*) as count FROM Events WHERE IsApproved = 1");
        $eventCount = $eventCountStmt->fetch();
        $summary['total_events'] = $eventCount['count'] ?? 0;
    } catch (Exception $e) {
        error_log("Failed to get event count: " . $e->getMessage());
    }

    try {
        // Get period bookings
        $bookingCountStmt = $pdo->prepare("SELECT COUNT(*) as count FROM Bookings WHERE BookingDate BETWEEN ? AND ?");
        $bookingCountStmt->execute([$startDate, $endDate]);
        $bookingCount = $bookingCountStmt->fetch();
        $summary['period_bookings'] = $bookingCount['count'] ?? 0;
    } catch (Exception $e) {
        error_log("Failed to get booking count: " . $e->getMessage());
    }

    try {
        // Get period revenue
        $revenueStmt = $pdo->prepare("
            SELECT COALESCE(SUM(p.Amount), 0) as revenue
            FROM Payments p
            JOIN Bookings b ON p.BookingId = b.BookingId
            WHERE b.BookingDate BETWEEN ? AND ?
        ");
        $revenueStmt->execute([$startDate, $endDate]);
        $revenue = $revenueStmt->fetch();
        $summary['period_revenue'] = $revenue['revenue'] ?? 0;
    } catch (Exception $e) {
        error_log("Failed to get revenue: " . $e->getMessage());
    }

    try {
        // Get pending events
        $pendingStmt = $pdo->query("SELECT COUNT(*) as count FROM Events WHERE IsApproved = 0");
        $pending = $pendingStmt->fetch();
        $summary['pending_events'] = $pending['count'] ?? 0;
    } catch (Exception $e) {
        error_log("Failed to get pending events: " . $e->getMessage());
    }

    // 5. TOP PERFORMING CATEGORIES (simplified query)
    try {
        $categoryQuery = "
            SELECT
                COALESCE(e.EventCategory, 'Other') as EventCategory,
                COUNT(DISTINCT b.BookingId) as bookings,
                COALESCE(SUM(p.Amount), 0) as revenue
            FROM Events e
            LEFT JOIN Bookings b ON e.EventId = b.EventId
            LEFT JOIN Payments p ON b.BookingId = p.BookingId
            WHERE b.BookingDate BETWEEN ? AND ? AND e.IsApproved = 1
            GROUP BY e.EventCategory
            ORDER BY revenue DESC
        ";
        $categoryStmt = $pdo->prepare($categoryQuery);
        $categoryStmt->execute([$startDate, $endDate]);
        $categoryData = $categoryStmt->fetchAll();
    } catch (Exception $e) {
        error_log("Category analytics query failed: " . $e->getMessage());
        $categoryData = [];
    }

} catch (Exception $e) {
    $error = "Error loading analytics: " . $e->getMessage();

    // Initialize fallback data to prevent undefined variable errors
    $summary = [
        'total_users' => 0,
        'total_events' => 0,
        'period_bookings' => 0,
        'period_revenue' => 0,
        'pending_events' => 0
    ];
    $salesData = [];
    $eventData = [];
    $userData = [];
    $categoryData = [];
}

// Ensure $summary is always defined with fallback values
if (!isset($summary) || !$summary) {
    $summary = [
        'total_users' => 0,
        'total_events' => 0,
        'period_bookings' => 0,
        'period_revenue' => 0,
        'pending_events' => 0
    ];
}

// Ensure other variables are defined
$salesData = $salesData ?? [];
$eventData = $eventData ?? [];
$userData = $userData ?? [];
$categoryData = $categoryData ?? [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard - Addis Tickets Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .nav-links {
            margin-top: 10px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .filters form {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .filters input, .filters select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card .icon {
            font-size: 40px;
            margin-bottom: 15px;
        }
        .stat-card .value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-card .label {
            color: #666;
            font-size: 14px;
        }
        .chart-container {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        .data-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .data-table h3 {
            background: #f8f9fa;
            padding: 20px;
            margin: 0;
            border-bottom: 1px solid #eee;
        }
        .data-table table {
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .data-table tr:hover {
            background: #f8f9fa;
        }
        .export-btn {
            background: #28a745;
            margin-left: 10px;
        }
        .export-btn:hover {
            background: #218838;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            .filters form {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-chart-line"></i> Analytics Dashboard</h1>
        <div class="nav-links">
            <a href="admin.php"><i class="fas fa-arrow-left"></i> Back to Admin</a>
            <a href="admin_reports.php"><i class="fas fa-file-alt"></i> Detailed Reports</a>
            <a href="admin_export.php"><i class="fas fa-download"></i> Export Data</a>
            <a href="admin_settings.php"><i class="fas fa-cog"></i> Settings</a>
        </div>
    </div>

    <div class="container">
        <?php if (isset($error)): ?>
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Date Range Filters -->
        <div class="filters">
            <form method="GET">
                <label>Period:</label>
                <select name="period" onchange="updateDateRange(this.value)">
                    <option value="week" <?= $period == 'week' ? 'selected' : '' ?>>Last 7 Days</option>
                    <option value="month" <?= $period == 'month' ? 'selected' : '' ?>>This Month</option>
                    <option value="quarter" <?= $period == 'quarter' ? 'selected' : '' ?>>This Quarter</option>
                    <option value="year" <?= $period == 'year' ? 'selected' : '' ?>>This Year</option>
                    <option value="custom" <?= $period == 'custom' ? 'selected' : '' ?>>Custom Range</option>
                </select>
                
                <label>From:</label>
                <input type="date" name="start_date" value="<?= $startDate ?>" id="startDate">
                
                <label>To:</label>
                <input type="date" name="end_date" value="<?= $endDate ?>" id="endDate">
                
                <button type="submit" class="btn">
                    <i class="fas fa-filter"></i> Apply Filter
                </button>
                
                <a href="admin_export.php?start_date=<?= $startDate ?>&end_date=<?= $endDate ?>" class="btn export-btn">
                    <i class="fas fa-download"></i> Export Report
                </a>
            </form>
        </div>

        <!-- Summary Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon" style="color: #667eea;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="value"><?= number_format($summary['total_users']) ?></div>
                <div class="label">Total Users</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #28a745;">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="value"><?= number_format($summary['total_events']) ?></div>
                <div class="label">Active Events</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #ffc107;">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="value"><?= number_format($summary['period_bookings']) ?></div>
                <div class="label">Period Bookings</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #dc3545;">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="value"><?= number_format($summary['period_revenue'], 2) ?> ETB</div>
                <div class="label">Period Revenue</div>
            </div>
            
            <div class="stat-card">
                <div class="icon" style="color: #fd7e14;">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="value"><?= number_format($summary['pending_events']) ?></div>
                <div class="label">Pending Approvals</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid-2">
            <!-- Revenue Chart -->
            <div class="chart-container">
                <h3><i class="fas fa-chart-line"></i> Daily Revenue Trend</h3>
                <div class="chart-wrapper">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>

            <!-- Category Performance -->
            <div class="chart-container">
                <h3><i class="fas fa-chart-pie"></i> Revenue by Category</h3>
                <div class="chart-wrapper">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Event Performance Table -->
        <div class="data-table">
            <h3><i class="fas fa-trophy"></i> Top Performing Events</h3>
            <table>
                <thead>
                    <tr>
                        <th>Event</th>
                        <th>Date</th>
                        <th>Organizer</th>
                        <th>Bookings</th>
                        <th>Revenue</th>
                        <th>Customers</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($eventData as $event): ?>
                    <tr>
                        <td>
                            <strong><?= htmlspecialchars($event['Title']) ?></strong><br>
                            <small><?= htmlspecialchars($event['Place']) ?></small>
                        </td>
                        <td><?= date('M d, Y', strtotime($event['EventDate'])) ?></td>
                        <td><?= htmlspecialchars($event['OrganizerName']) ?></td>
                        <td><?= number_format($event['total_bookings']) ?></td>
                        <td><?= number_format($event['event_revenue'], 2) ?> ETB</td>
                        <td><?= number_format($event['unique_customers']) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [<?php 
                    $labels = [];
                    foreach ($salesData as $sale) {
                        $labels[] = "'" . date('M d', strtotime($sale['booking_date'])) . "'";
                    }
                    echo implode(',', array_reverse($labels));
                ?>],
                datasets: [{
                    label: 'Daily Revenue (ETB)',
                    data: [<?php 
                        $revenues = [];
                        foreach ($salesData as $sale) {
                            $revenues[] = $sale['total_revenue'] ?? 0;
                        }
                        echo implode(',', array_reverse($revenues));
                    ?>],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Category Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryChart = new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php 
                    $catLabels = [];
                    foreach ($categoryData as $cat) {
                        $catLabels[] = "'" . htmlspecialchars($cat['EventCategory']) . "'";
                    }
                    echo implode(',', $catLabels);
                ?>],
                datasets: [{
                    data: [<?php 
                        $catRevenues = [];
                        foreach ($categoryData as $cat) {
                            $catRevenues[] = $cat['revenue'] ?? 0;
                        }
                        echo implode(',', $catRevenues);
                    ?>],
                    backgroundColor: [
                        '#667eea', '#764ba2', '#f093fb', '#f5576c', 
                        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Date range updater
        function updateDateRange(period) {
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');
            const today = new Date();
            
            switch(period) {
                case 'week':
                    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    startDate.value = weekAgo.toISOString().split('T')[0];
                    break;
                case 'month':
                    startDate.value = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                    break;
                case 'quarter':
                    const quarter = Math.floor(today.getMonth() / 3);
                    startDate.value = new Date(today.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
                    break;
                case 'year':
                    startDate.value = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                    break;
            }
            
            if (period !== 'custom') {
                endDate.value = today.toISOString().split('T')[0];
            }
        }
    </script>
</body>
</html>
