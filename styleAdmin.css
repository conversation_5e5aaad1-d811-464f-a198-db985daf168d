:root {
    --primary: #f15a22; /* Ethiopian orange */
    --primary-light: #ff7a45;
    --primary-dark: #d13a00;
    --secondary: #078930; /* Ethiopian green */
    --secondary-light: #2aa952;
    --secondary-dark: #056923;
    --light: #f8f9fa;
    --dark: #2c3e50;
    --gray: #7f8c8d;
    --light-gray: #ecf0f1;
    --warning: #f39c12;
    --danger: #e74c3c;
    --success: #2ecc71;
    --info: #3498db;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f7fa;
    min-height: 100vh;
    padding: 20px;
    color: var(--dark);
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background-color: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

h1 {
    color: var(--primary);
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

h1::before {
    content: "";
    display: inline-block;
    width: 10px;
    height: 40px;
    background: var(--secondary);
    border-radius: 5px;
}

h2 {
    color: var(--dark);
    font-size: 1.5rem;
    margin-bottom: 15px;
    font-weight: 500;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--light-gray);
}

/* Tabs Navigation */
.tabs-navigation {
    display: flex;
    border-bottom: 2px solid var(--light-gray);
    margin-bottom: 25px;
    gap: 5px;
}

.tab-btn {
    padding: 12px 25px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray);
    position: relative;
    transition: all 0.3s ease;
    border-radius: 8px 8px 0 0;
    font-size: 0.95rem;
}

.tab-btn:hover {
    color: var(--primary);
    background: rgba(241, 90, 34, 0.1);
}

.tab-btn.active {
    color: var(--primary);
    background: white;
    font-weight: 600;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary);
}

/* Stats Cards */
.stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-box {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-top: 4px solid var(--primary);
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.stat-box:nth-child(1) { border-top-color: var(--primary); }
.stat-box:nth-child(2) { border-top-color: var(--secondary); }
.stat-box:nth-child(3) { border-top-color: var(--info); }
.stat-box:nth-child(4) { border-top-color: var(--warning); }

.stat-box h2 {
    font-size: 1rem;
    margin-bottom: 15px;
    color: var(--gray);
    font-weight: 500;
    border: none;
    padding: 0;
}

.stat-box p {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark);
    margin: 0;
}

/* Tables */
.table-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    overflow: hidden;
    margin-bottom: 30px;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--light-gray);
}

th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--dark);
    cursor: pointer;
    position: sticky;
    top: 0;
    transition: background 0.2s ease;
}

th:hover {
    background-color: #dfe6e9;
}

tr {
    transition: background 0.2s ease;
}

tr:hover {
    background-color: rgba(241, 90, 34, 0.05);
}

/* Action Buttons */
.action-btn {
    padding: 8px 15px;
    border-radius: 6px;
    border: none;
    font-size: 0.85rem;
    cursor: pointer;
    margin-right: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.action-btn i {
    font-size: 0.9rem;
}

.delete-btn {
    background-color: var(--danger);
    color: white
}

.delete-btn:hover {
    background-color: #ce0913;
}
.delete-btn {
    background-color: #e2646b;
}
.approve-btn {
    background-color: #95eb7b;
}

.approve-btn:hover {
    background-color: #30be05;
}
.reject-btn {
    background-color: #f57777;
}
.reject-btn:hover {
    background-color: #ce0808;
}

.update-btn {
    background-color: var(--primary);
    color: white;
}

.update-btn:hover {
    background-color: var(--primary-dark);
}

.add-btn {
    background-color: var(--success);
    color: white;
}

.add-btn:hover {
    background-color: #27ae60;
}

/* Tab Content */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-content.active {
    display: block;
}

/* Filter/Search Controls */
.controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.search-box {
    display: flex;
    align-items: center;
    flex-grow: 1;
    min-width: 250px;
}

.search-box input {
    padding: 10px 15px;
    border: 1px solid var(--light-gray);
    border-radius: 8px;
    margin-right: 10px;
    flex-grow: 1;
    transition: border 0.2s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(241, 90, 34, 0.2);
}

.filter-select {
    padding: 10px 15px;
    border: 1px solid var(--light-gray);
    border-radius: 8px;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(241, 90, 34, 0.2);
}

/* Status Badges */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-available {
    background-color: rgba(46, 204, 113, 0.15);
    color: var(--success);
}

status-reserved {
    background-color: rgba(243, 156, 18, 0.15);
    color: var(--warning);
}

.status-cancelled {
    background-color: rgba(231, 76, 60, 0.15);
    color: var(--danger);
}

/* Type Badges */
.type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    background-color: rgba(52, 152, 219, 0.15);
    color: var(--info);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form Elements */
select {
    padding: 8px 12px;
    border: 1px solid var(--light-gray);
    border-radius: 6px;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(241, 90, 34, 0.2);
}

input[type="datetime-local"] {
    padding: 8px 12px;
    border: 1px solid var(--light-gray);
    border-radius: 6px;
    transition: all 0.2s ease;
}

input[type="datetime-local"]:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(241, 90, 34, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
    
    .tabs-navigation {
        overflow-x: auto;
        padding-bottom: 5px;
    }
    
    .tab-btn {
        padding: 10px 15px;
        font-size: 0.85rem;
    }
    
    .controls {
        flex-direction: column;
    }
    
    .search-box {
        width: 100%;
    }
}