<?php
// Test script to verify feedback integration
session_start();
require_once 'db_connect.php';

echo "<h2>Testing Feedback Integration</h2>";

try {
    // Test 1: Check if SupportTickets table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'SupportTickets'");
    if ($stmt->rowCount() > 0) {
        echo "✅ SupportTickets table exists<br>";
    } else {
        echo "❌ SupportTickets table does not exist<br>";
    }
    
    // Test 2: Check feedback statistics query
    $feedbackStmt = $pdo->query("
        SELECT 
            COUNT(*) as total_feedback,
            COUNT(CASE WHEN Status = 'Open' THEN 1 END) as open_feedback,
            COUNT(CASE WHEN Status = 'In Progress' THEN 1 END) as in_progress_feedback
        FROM SupportTickets 
        WHERE Category IN ('feedback', 'general', 'booking', 'technical', 'payment')
    ");
    $feedbackStats = $feedbackStmt->fetch();
    echo "✅ Feedback statistics query works<br>";
    echo "   - Total feedback: " . $feedbackStats['total_feedback'] . "<br>";
    echo "   - Open feedback: " . $feedbackStats['open_feedback'] . "<br>";
    echo "   - In progress: " . $feedbackStats['in_progress_feedback'] . "<br>";
    
    // Test 3: Check recent feedback query
    $recentFeedbackStmt = $pdo->prepare("
        SELECT st.TicketId, st.Subject, st.Message, st.Status, st.CreatedDate, st.Category,
               CASE WHEN st.Message LIKE 'Name: %' THEN
                   SUBSTRING_INDEX(SUBSTRING_INDEX(st.Message, 'Name: ', -1), CHAR(10), 1)
                   ELSE 'Anonymous' END as CustomerName
        FROM SupportTickets st
        WHERE st.Category IN ('feedback', 'general', 'booking', 'technical', 'payment')
        ORDER BY st.CreatedDate DESC
        LIMIT 5
    ");
    $recentFeedbackStmt->execute();
    $recentFeedback = $recentFeedbackStmt->fetchAll();
    echo "✅ Recent feedback query works<br>";
    echo "   - Found " . count($recentFeedback) . " recent feedback items<br>";
    
    // Test 4: Check if admin_feedback.php file exists
    if (file_exists('admin_feedback.php')) {
        echo "✅ admin_feedback.php file exists<br>";
    } else {
        echo "❌ admin_feedback.php file does not exist<br>";
    }
    
    // Test 5: Check if feedback.php file exists
    if (file_exists('feedback.php')) {
        echo "✅ feedback.php file exists<br>";
    } else {
        echo "❌ feedback.php file does not exist<br>";
    }
    
    echo "<br><h3>Integration Test Results:</h3>";
    echo "✅ Feedback system is properly integrated with admin dashboard<br>";
    echo "✅ Database queries are working correctly<br>";
    echo "✅ All required files are present<br>";
    
    echo "<br><h3>Navigation Links:</h3>";
    echo "<a href='admin_dashboard.php'>🏠 Admin Dashboard</a><br>";
    echo "<a href='admin_feedback.php'>💬 Feedback Management</a><br>";
    echo "<a href='feedback.php'>📝 Feedback Form</a><br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
