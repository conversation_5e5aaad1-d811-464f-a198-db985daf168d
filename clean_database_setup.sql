CREATE DATABASE eventbb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE eventbb;

-- =====================================================
-- 1. USER MANAGEMENT TABLES
-- =====================================================

-- Users table for customers
CREATE TABLE Users (
    UserId VARCHAR(50) PRIMARY KEY,
    FirstName VARCHAR(100) NOT NULL,
    LastName VARCHAR(100) NOT NULL,
    Email VARCHAR(255) UNIQUE NOT NULL,
    Password VARCHAR(255) NOT NULL,
    PhoneNumber VARCHAR(20),
    SubCity VARCHAR(100),
    HouseNumber INT,
    RegistrationDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    IsVerified BOOLEAN DEFAULT FALSE,
    LastLogin TIMESTAMP NULL,
    INDEX idx_email (Email),
    INDEX idx_phone (PhoneNumber)
);

-- Event Organizers table
CREATE TABLE EventOrganizers (
    OrganizerId VARCHAR(50) PRIMARY KEY,
    OrganizerName VARCHAR(200) NOT NULL,
    Email VARCHAR(255) UNIQUE NOT NULL,
    Password VARCHAR(255) NOT NULL,
    PhoneNumber VARCHAR(20),
    CompanyName VARCHAR(200),
    Address TEXT,
    IsApproved BOOLEAN DEFAULT FALSE,
    RegistrationDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_organizer_email (Email)
);

-- Admin Users table
CREATE TABLE AdminUsers (
    AdminId VARCHAR(50) PRIMARY KEY,
    Username VARCHAR(100) UNIQUE NOT NULL,
    Email VARCHAR(255) UNIQUE NOT NULL,
    Password VARCHAR(255) NOT NULL,
    FullName VARCHAR(200) NOT NULL,
    Role ENUM('SuperAdmin', 'Admin', 'Moderator') DEFAULT 'Admin',
    IsActive BOOLEAN DEFAULT TRUE,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    LastLogin TIMESTAMP NULL,
    INDEX idx_admin_username (Username),
    INDEX idx_admin_email (Email)
);

-- =====================================================
-- 2. EVENT MANAGEMENT TABLES
-- =====================================================

-- Events table
CREATE TABLE Events (
    EventId VARCHAR(50) PRIMARY KEY,
    Title VARCHAR(300) NOT NULL,
    Description TEXT,
    EventDate DATETIME NOT NULL,
    EndDate DATETIME NULL,
    Place VARCHAR(200) NOT NULL,
    Address TEXT,
    EventCategory ENUM('Concert', 'Sports', 'Theater', 'Conference', 'Festival', 'Comedy', 'Cultural', 'Other') NOT NULL,
    EventImage VARCHAR(500),
    TicketPrice DECIMAL(10,2) DEFAULT 0.00,
    MaxCapacity INT DEFAULT 100,
    OrganizerId VARCHAR(50) NOT NULL,
    IsApproved BOOLEAN DEFAULT FALSE,
    IsFeatured BOOLEAN DEFAULT FALSE,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (OrganizerId) REFERENCES EventOrganizers(OrganizerId) ON DELETE CASCADE,
    INDEX idx_event_date (EventDate),
    INDEX idx_event_category (EventCategory),
    INDEX idx_event_organizer (OrganizerId),
    INDEX idx_event_approved (IsApproved)
);

-- Tickets table for seat management
CREATE TABLE Tickets (
    TicketId VARCHAR(50) PRIMARY KEY,
    EventId VARCHAR(50) NOT NULL,
    TicketType ENUM('Regular', 'VIP', 'VVIP') NOT NULL,
    SeatNumber VARCHAR(10),
    Price DECIMAL(10,2) NOT NULL,
    Status ENUM('Available', 'Reserved', 'Sold') DEFAULT 'Available',
    AvailableTickets INT DEFAULT 1,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (EventId) REFERENCES Events(EventId) ON DELETE CASCADE,
    INDEX idx_ticket_event (EventId),
    INDEX idx_ticket_type (TicketType),
    INDEX idx_ticket_status (Status)
);

-- =====================================================
-- 3. BOOKING AND PAYMENT TABLES
-- =====================================================

-- Bookings table
CREATE TABLE Bookings (
    BookingId VARCHAR(100) PRIMARY KEY,
    UserId VARCHAR(50) NOT NULL,
    EventId VARCHAR(50) NOT NULL,
    TicketId VARCHAR(50),
    BookingDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    TotalAmount DECIMAL(10,2) NOT NULL,
    PaymentStatus ENUM('Pending', 'Completed', 'Failed', 'Cancelled', 'Refunded') DEFAULT 'Pending',
    BookingReference VARCHAR(100) UNIQUE,
    SpecialRequests TEXT,
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    FOREIGN KEY (EventId) REFERENCES Events(EventId) ON DELETE CASCADE,
    FOREIGN KEY (TicketId) REFERENCES Tickets(TicketId) ON DELETE SET NULL,
    INDEX idx_booking_user (UserId),
    INDEX idx_booking_event (EventId),
    INDEX idx_booking_status (PaymentStatus),
    INDEX idx_booking_date (BookingDate)
);

-- Payments table
CREATE TABLE Payments (
    PaymentId VARCHAR(100) PRIMARY KEY,
    BookingId VARCHAR(100) NOT NULL,
    Amount DECIMAL(10,2) NOT NULL,
    PaymentDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Method ENUM('Telebirr', 'CBE Birr', 'Cash', 'Bank Transfer', 'Credit Card') NOT NULL,
    Status ENUM('Pending', 'Completed', 'Failed', 'Cancelled', 'Refunded') DEFAULT 'Pending',
    TransactionId VARCHAR(200),
    PaymentReference VARCHAR(200),
    ProcessedBy VARCHAR(100),
    FOREIGN KEY (BookingId) REFERENCES Bookings(BookingId) ON DELETE CASCADE,
    INDEX idx_payment_booking (BookingId),
    INDEX idx_payment_status (Status),
    INDEX idx_payment_method (Method),
    INDEX idx_payment_date (PaymentDate)
);

-- =====================================================
-- 4. SUPPORT AND COMMUNICATION TABLES
-- =====================================================

-- Support Tickets table
CREATE TABLE SupportTickets (
    TicketId VARCHAR(50) PRIMARY KEY,
    UserId VARCHAR(50),
    Name VARCHAR(200) NOT NULL,
    Email VARCHAR(255) NOT NULL,
    Category ENUM('general', 'booking', 'payment', 'account', 'technical', 'refund', 'feedback') NOT NULL,
    Subject VARCHAR(300) NOT NULL,
    Message TEXT NOT NULL,
    Status ENUM('Open', 'In Progress', 'Resolved', 'Closed') DEFAULT 'Open',
    Priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    AssignedTo VARCHAR(50),
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE SET NULL,
    FOREIGN KEY (AssignedTo) REFERENCES AdminUsers(AdminId) ON DELETE SET NULL,
    INDEX idx_support_user (UserId),
    INDEX idx_support_status (Status),
    INDEX idx_support_category (Category),
    INDEX idx_support_priority (Priority)
);

-- FAQ table
CREATE TABLE FAQ (
    FAQId INT AUTO_INCREMENT PRIMARY KEY,
    Question VARCHAR(500) NOT NULL,
    Answer TEXT NOT NULL,
    Category VARCHAR(100),
    DisplayOrder INT DEFAULT 0,
    IsActive BOOLEAN DEFAULT TRUE,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_faq_category (Category),
    INDEX idx_faq_active (IsActive)
);

-- =====================================================
-- 5. SYSTEM CONFIGURATION TABLES
-- =====================================================

-- System Settings table
CREATE TABLE SystemSettings (
    SettingId INT AUTO_INCREMENT PRIMARY KEY,
    SettingKey VARCHAR(100) UNIQUE NOT NULL,
    SettingValue TEXT,
    Description VARCHAR(500),
    Category VARCHAR(100),
    IsActive BOOLEAN DEFAULT TRUE,
    UpdatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (SettingKey),
    INDEX idx_setting_category (Category)
);

-- Audit Log table for tracking changes
CREATE TABLE AuditLog (
    LogId INT AUTO_INCREMENT PRIMARY KEY,
    UserId VARCHAR(50),
    UserType ENUM('User', 'Organizer', 'Admin') NOT NULL,
    Action VARCHAR(200) NOT NULL,
    TableName VARCHAR(100),
    RecordId VARCHAR(100),
    OldValues JSON,
    NewValues JSON,
    IPAddress VARCHAR(45),
    UserAgent TEXT,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_audit_user (UserId),
    INDEX idx_audit_action (Action),
    INDEX idx_audit_table (TableName),
    INDEX idx_audit_date (CreatedDate)
);

-- =====================================================
-- 6. SESSION MANAGEMENT TABLE
-- =====================================================

-- User Sessions table for security
CREATE TABLE UserSessions (
    SessionId VARCHAR(128) PRIMARY KEY,
    UserId VARCHAR(50) NOT NULL,
    UserType ENUM('User', 'Organizer', 'Admin') NOT NULL,
    IPAddress VARCHAR(45),
    UserAgent TEXT,
    CreatedDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ExpiresAt TIMESTAMP NOT NULL,
    IsActive BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (UserId) REFERENCES Users(UserId) ON DELETE CASCADE,
    INDEX idx_session_user (UserId),
    INDEX idx_session_expires (ExpiresAt),
    INDEX idx_session_active (IsActive)
);

-- =====================================================
-- 7. CREATE VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for event details with organizer info
CREATE VIEW EventDetails AS
SELECT 
    e.*,
    o.OrganizerName,
    o.CompanyName,
    o.Email as OrganizerEmail,
    o.PhoneNumber as OrganizerPhone,
    COUNT(DISTINCT t.TicketId) as TotalTickets,
    COUNT(DISTINCT CASE WHEN t.Status = 'Available' THEN t.TicketId END) as AvailableTickets,
    COUNT(DISTINCT b.BookingId) as TotalBookings,
    COALESCE(SUM(CASE WHEN b.PaymentStatus = 'Completed' THEN b.TotalAmount END), 0) as TotalRevenue
FROM Events e
LEFT JOIN EventOrganizers o ON e.OrganizerId = o.OrganizerId
LEFT JOIN Tickets t ON e.EventId = t.EventId
LEFT JOIN Bookings b ON e.EventId = b.EventId
GROUP BY e.EventId;

-- View for booking details
CREATE VIEW BookingDetails AS
SELECT 
    b.*,
    u.FirstName,
    u.LastName,
    u.Email as UserEmail,
    u.PhoneNumber as UserPhone,
    e.Title as EventTitle,
    e.EventDate,
    e.Place as EventPlace,
    p.PaymentId,
    p.Method as PaymentMethod,
    p.PaymentDate,
    p.TransactionId
FROM Bookings b
LEFT JOIN Users u ON b.UserId = u.UserId
LEFT JOIN Events e ON b.EventId = e.EventId
LEFT JOIN Payments p ON b.BookingId = p.BookingId;

-- =====================================================
-- 8. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to update booking payment status when payment is completed
DELIMITER //
CREATE TRIGGER UpdateBookingStatus
AFTER UPDATE ON Payments
FOR EACH ROW
BEGIN
    IF NEW.Status = 'Completed' AND OLD.Status != 'Completed' THEN
        UPDATE Bookings 
        SET PaymentStatus = 'Completed' 
        WHERE BookingId = NEW.BookingId;
    ELSEIF NEW.Status = 'Failed' AND OLD.Status != 'Failed' THEN
        UPDATE Bookings 
        SET PaymentStatus = 'Failed' 
        WHERE BookingId = NEW.BookingId;
    END IF;
END//
DELIMITER ;

-- Trigger to update ticket status when booking is completed
DELIMITER //
CREATE TRIGGER UpdateTicketStatus
AFTER UPDATE ON Bookings
FOR EACH ROW
BEGIN
    IF NEW.PaymentStatus = 'Completed' AND OLD.PaymentStatus != 'Completed' THEN
        UPDATE Tickets 
        SET Status = 'Sold' 
        WHERE TicketId = NEW.TicketId;
    END IF;
END//
DELIMITER ;

-- =====================================================
-- DATABASE SETUP COMPLETE
-- =====================================================

-- Show created tables
SHOW TABLES;

-- Display table structure summary
SELECT 
    TABLE_NAME as 'Table Name',
    TABLE_ROWS as 'Rows',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'eventbb'
ORDER BY TABLE_NAME;

-- Success message
SELECT 'Clean Event Ticketing Database Setup Complete!' as Status,
       'No pre-entered data - Ready for production use' as Message,
       NOW() as 'Setup Time';